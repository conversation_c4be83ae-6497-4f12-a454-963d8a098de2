package com.morningstar.martapi.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

public class S3Service {

    private final String defaultRegion;
    private static final Logger LOGGER = LoggerFactory.getLogger(S3Service.class);
    private final AmazonS3 amazonS3;
    private final String inputBucket;
    private final String usOutputBucket;
    private final String euOutputBucket;
    private final S3PreSignerProvider s3PreSignerProvider;

    public S3Service(AmazonS3 amazonS3, String inputBucket, String outputBucket, String euOutputBucket, String region, S3PreSignerProvider s3PreSignerProvider) {
        this.amazonS3 = amazonS3;
        this.inputBucket = inputBucket;
        this.usOutputBucket = outputBucket;
        this.euOutputBucket = euOutputBucket;
        this.defaultRegion = region;
        this.s3PreSignerProvider = s3PreSignerProvider;
    }

    public void putObject(String key, String object, ObjectMetadata objectMetadata) {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(object.getBytes(StandardCharsets.UTF_8))) {
            Instant instant = Instant.now();
            PutObjectRequest putObjectRequest = new PutObjectRequest(inputBucket, key, inputStream, objectMetadata);
            amazonS3.putObject(putObjectRequest);
            LOGGER.info("Put object {} to S3 succeed, total time: {}ms", key, Duration.between(instant, Instant.now()).toMillis());
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    // A presigned url must be requested by a client or object registered in the same region as the bucket containing the data.
    // Sample AWS Exception Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
    public PresignedGetObjectRequest getPresignedUrl(String key, String regionOfData) {
        String region = StringUtils.isEmpty(regionOfData) ? defaultRegion : regionOfData;
        S3Presigner signer = s3PreSignerProvider.getS3Presigner(region);
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(getBucketForRegion(region))
                .key(key)
                .build();
        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(60L * 2))
                .getObjectRequest(getObjectRequest)
                .build();
        return signer.presignGetObject(presignRequest);
    }

    private String getBucketForRegion(String region) {
        if (StringUtils.isNotEmpty(region) && region.startsWith("eu-")) {
            return euOutputBucket;
        } else {
            return usOutputBucket;
        }
    }
}
