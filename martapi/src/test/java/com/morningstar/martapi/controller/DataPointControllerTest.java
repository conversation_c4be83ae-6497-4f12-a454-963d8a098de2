package com.morningstar.martapi.controller;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class DataPointControllerTest {

    private DataPointController dataPointController;

    private MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic;

    @Before
    public void setUp(){
        dataPointRepositoryMockedStatic = Mockito.mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
        dataPointController = new DataPointController();
    }

    @After
    public void after(){
        dataPointRepositoryMockedStatic.close();
    }


    @Test
    public void testGetId() {
        DataPoint dp = DataPoint.builder().id("123").build();
        when(DataPointRepository.getByNid(anyString())).thenReturn(dp);
        Mono<String> result1 = dataPointController.getDatapointValues("123", "", "", "", "");
        StepVerifier.create(result1).expectSubscription().assertNext(d -> {
                    assertNotNull(d.getBytes());
                })
                .expectComplete()
                .verify();

        when(DataPointRepository.getByNid(anyString())).thenReturn(null);
        Mono<String> result2 = dataPointController.getDatapointValues("123", "", "", "", "");
        StepVerifier.create(result2).expectSubscription().assertNext(d -> {
                    assertEquals("[ null ]", d);
                })
                .expectComplete()
                .verify();

    }
}
