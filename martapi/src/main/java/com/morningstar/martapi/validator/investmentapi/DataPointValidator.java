package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martapi.validator.entity.DataPointConfiguration;
import com.morningstar.martequity.validation.EquityRequestValidation;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DataPointValidator implements Validator<InvestmentApiRequest> {

    EquityRequestValidation equityRequestValidation = new EquityRequestValidation();

    @Override
    public void validate(InvestmentApiRequest request) throws InvestmentApiValidationException {
        List<GridviewDataPoint> dataPoints = request.getDataPoints();
        if (CollectionUtils.isEmpty(dataPoints)) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - dataPoints");
            throw new InvestmentApiValidationException(status);
        }
        Set<String> distinctIdentifiers = new HashSet<>();
        Set<DataPointConfiguration> distinctDataPointConfig = new HashSet<>();
        for (GridviewDataPoint dataPoint : dataPoints) {
            validateDataPointStructure(dataPoint);

            if (CollectionUtils.isEmpty(dataPoint.getDataPointIds())) {
                distinctIdentifiers(distinctIdentifiers, dataPoint);
            }

            if (!isEquityDataPoint(dataPoint) &&!distinctDataPointConfig.add(DataPointConfiguration.extract(dataPoint))) {
                throw new InvestmentApiValidationException(Status.DUPLICATE_DATAPOINT
                        .withMessage("Duplicate data point - %s" + dataPoint.getDataPointId()));
            }
        }

        List<String> validateResponse = equityRequestValidation.validateDataPointRequest(dataPoints);
        if(CollectionUtils.isNotEmpty(validateResponse)) {
            Status status = Status.INVALID_DATAPOINT
                    .withMessage(String.format("Invalid datapoint parameters for datapointIds: %s", String.join(",", validateResponse)));
            throw new InvestmentApiValidationException(status);
        }
    }

    private void validateDataPointStructure(GridviewDataPoint dataPoint) {
        if (isRequestMissingDatapointIdAttribute(dataPoint)) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - dataPointId");
            throw new InvestmentApiValidationException(status);
        }

        if(isRequestContainsBothDatapointIdAttribute(dataPoint)){
            Status status = Status.INVALID_DATAPOINT_REQUEST_FORMAT;
            throw new InvestmentApiValidationException(status);
        }

        if (StringUtils.isWhitespace(dataPoint.getAlias())) {
            Status status = Status.BLANK_ALIAS;
            throw new InvestmentApiValidationException(status);
        }
    }

    private void distinctIdentifiers(Set<String> distinctIdentifiers, GridviewDataPoint dataPoint) {
        String identifier = dataPoint.getAlias() != null ? dataPoint.getAlias() : dataPoint.getDataPointId();

        if (distinctIdentifiers.contains(identifier)) {
            String errorType = dataPoint.getAlias() != null
                    ? "Alias '" + identifier + "' conflicts with existing identifier"
                    : "Data point ID '" + identifier + "' conflicts with existing identifier";
            throw new InvestmentApiValidationException(Status.DUPLICATE_DATAPOINT
                    .withMessage(errorType));
        }
        distinctIdentifiers.add(identifier);
    }

    private boolean isEquityDataPoint(GridviewDataPoint dataPoint) {
        return CollectionUtils.isNotEmpty(dataPoint.getDataPointIds());
    }

    private boolean isRequestContainsBothDatapointIdAttribute(GridviewDataPoint dataPoint){
        return StringUtils.isNotEmpty(dataPoint.getDataPointId()) && CollectionUtils.isNotEmpty(dataPoint.getDataPointIds());
    }

    private boolean isRequestMissingDatapointIdAttribute(GridviewDataPoint dataPoint){
        return StringUtils.isEmpty(dataPoint.getDataPointId()) && CollectionUtils.isEmpty(dataPoint.getDataPointIds());
    }
}