package com.morningstar.martapi.service;

import static com.morningstar.martapi.config.Constant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.SdkClientException;
import com.morningstar.dataac.martgateway.core.async.entity.GridViewAsyncInput;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.entity.AsyncCacheMessage;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.repo.DynamoDao;
import com.morningstar.martapi.util.AsyncGetStatusUserIdUtil;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.async.entity.AsyncDbDetails;
import com.morningstar.dataac.martgateway.core.async.entity.AsyncInput;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

@RunWith(MockitoJUnitRunner.class)
public class AsyncApiServiceTest {

    private AsyncApiService asyncApiService;
    @Mock
    private S3Service s3Service;
    @Mock
    private LambdaService lambdaService;
    @Mock
    private RedisService redisService;
    @Mock
    private DynamoDao dynamoDao;
    @Spy
    private AsyncGetStatusUserIdUtil asyncGetStatusUserIdUtil;

    private static final String MOCK_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1FWXlOakZDTVRVME0wRkdSRGxCUTBVeE56RTFRamt6TWtaR1JUTTJOME01TlVZelJFWTJOdyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ULSU3VZx76Xxk9tG6qxUlm2x_ABohvUjsK-STjEINb7Ju_RsOMAmCbNL5r2DYlHFjVbSaf0sikNpQCNQuT2x5k3psA4fhxyqy7nP346fogDVFinyINzVBhw2OfO9wzLuuWLWDSM94tV_oStfcfaaYonwpSO_0kD8ey9wOqlfPYKoJbheXQa014qldmIv-EBVWUj0Zd0NsWu4JbWt9SD-dh9lpZMcErM5i58YNgXkKA_O0MvbsVJrtbQpTpiPXbCTuhhY6WTWRUcT7NOSnLGuHSsE5fj6bmZb9ei2DfSmqJRS9Ft2-gDdlmJ-1ZaHEcEjtzEtuEohYmRtQ7Vhljju3w";
    private static final String MOCK_TOKEN_USERID = "9818A9C6-109D-49FE-945F-8F8B9FCC7E34";
    private static final String ENVIRONMENT = "stg";

    @Before
    public void setup() {
        asyncApiService = new AsyncApiService(redisService, s3Service, lambdaService, asyncGetStatusUserIdUtil, dynamoDao, ENVIRONMENT, "us-east-1");
    }

    @Test
    public void testFetchAsyncData() {
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointId("dp1");
        Investment iv = new Investment("OPPPOONH");
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .dataPoints(Arrays.asList(dp))
                .investments(Arrays.asList(iv))
                .build();
        AsyncInput asyncInput = GridViewAsyncInput.builder()
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .investmentApiRequest(request)
                .build();
        AsyncApiResponseEntity response = asyncApiService.fetchAsyncData(asyncInput);
        Assert.assertEquals(SUBMITTED, response.getJobStatus());
    }

    @Test
    public void uploadToS3Error() {
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointId("dp1");
        Investment iv = new Investment("OPPPOONH");
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .dataPoints(Arrays.asList(dp))
                .investments(Arrays.asList(iv))
                .build();
        AsyncInput asyncInput = GridViewAsyncInput.builder()
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .investmentApiRequest(request)
                .build();
        doThrow(new SdkClientException("s3 error")).when(s3Service).putObject(any(), any(), any());
        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.fetchAsyncData(asyncInput)
        );
        Assert.assertEquals(Status.INTERNAL_ERROR, exception.getStatus());
    }

    @Test
    public void getStatusSuccess() throws MalformedURLException {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("9818A9C6-109D-49FE-945F-8F8B9FCC7E34")
                .url("decoy.com")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId("9818A9C6-109D-49FE-945F-8F8B9FCC7E34")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(String.valueOf(AsyncInput.ApiType.INVESTMENT_API))
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage)).thenReturn(null);
        PresignedGetObjectRequest presignedGetObjectRequest = mock(PresignedGetObjectRequest.class);
        URL url = new URL("https://www.decoy.com");
        when(presignedGetObjectRequest.url()).thenReturn(url);
        when(presignedGetObjectRequest.expiration()).thenReturn(Instant.now());
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest);
        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);
        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, null);
        Assert.assertEquals("decoy.com", response.getUrl());

        AsyncApiResponseEntity dbresponse = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, "");
        Assert.assertEquals("https://www.decoy.com", dbresponse.getUrl());
    }

    @Test
    public void getStatusSuccessCacheUrlDNE() throws MalformedURLException {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("9818A9C6-109D-49FE-945F-8F8B9FCC7E34")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        PresignedGetObjectRequest presignedGetObjectRequest = mock(PresignedGetObjectRequest.class);
        URL url = new URL("https://www.decoy.com");
        when(presignedGetObjectRequest.url()).thenReturn(url);
        when(presignedGetObjectRequest.expiration()).thenReturn(Instant.now());
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest);
        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID);
        Assert.assertEquals("https://www.decoy.com", response.getUrl());
        verify(redisService, times(1)).addRedisKey(any(), any());
    }

    @Test
    public void getJobNotFound() {
        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, "job", MOCK_TOKEN_USERID)
        );
        Assert.assertEquals(Status.JOB_ID_NOT_FOUND, exception.getStatus());
    }

    @Test
    public void getStatusInProgress() {
        String startTime = "2024-03-11T21:33:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("9818A9C6-109D-49FE-945F-8F8B9FCC7E34")
                .productId("product1")
                .status("Running")
                .startTime(startTime)
                .environment(ENVIRONMENT)
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID);
        Assert.assertEquals("Running", response.getJobStatus());
    }

    @Test
    public void getStatusInProgressInactiveJob() {
        String startTime = "2024-03-11T21:33:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("9818A9C6-109D-49FE-945F-8F8B9FCC7E34")
                .productId("product1")
                .status("Running")
                .startTime(startTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.isJobInactive(any())).thenReturn(true);
        when(dynamoDao.getAsyncRecord(any())).thenReturn(new AsyncDbDetails());
        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID);
        Assert.assertEquals("Fail", response.getJobStatus());
    }

    @Test
    public void getStatusWrongUser() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("randomUserId")
                .url("decoy.com")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .build();
        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId("randomUserId")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(String.valueOf(AsyncInput.ApiType.INVESTMENT_API))
                .build();
        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);

        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID)
        );
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage)).thenReturn(null);
        Assert.assertEquals(Status.UNAUTHORIZED_USER_FOR_JOB_ID, exception.getStatus());

        exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID)
        );
        Assert.assertEquals(Status.UNAUTHORIZED_USER_FOR_JOB_ID, exception.getStatus());
    }

    @Test
    public void getStatusProductIdMismatch() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .url("decoy.com")
                .productId("product")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .build();
        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(String.valueOf(AsyncInput.ApiType.INVESTMENT_API))
                .build();
        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);
        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, "")
        );
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage)).thenReturn(null);

        Assert.assertEquals(Status.PRODUCT_ID_MISMATCH, exception.getStatus());
        exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, "")
        );
        Assert.assertEquals(Status.PRODUCT_ID_MISMATCH, exception.getStatus());
    }

    @Test
    public void getStatusS3Exception() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(String.valueOf(AsyncInput.ApiType.INVESTMENT_API))
                .build();
        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage)).thenReturn(null);
        when(s3Service.getPresignedUrl(any(), any())).thenThrow(new SdkClientException("s3 error"));

        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID)
        );
        Assert.assertEquals(Status.INTERNAL_ERROR, exception.getStatus());

        exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID)
        );
        Assert.assertEquals(Status.INTERNAL_ERROR, exception.getStatus());
    }

    @Test
    public void getStatusWithServiceAccount() {
        when(asyncGetStatusUserIdUtil.determineUserId(any(), any())).thenReturn("headerUserId");

        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId("headerUserId")
                .url("decoy.com")
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, "headerUserId");
        Assert.assertEquals("decoy.com", response.getUrl());
    }

    @Test
    public void getStatusEnvironmentMismatch() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .url("decoy.com")
                .productId("product")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment("qa")
                .build();
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getGridviewStatus("product", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID)
        );
        Assert.assertEquals(Status.JOB_ID_NOT_FOUND, exception.getStatus());
    }

    @Test
    public void testHoldingApiSetsS3UrlsWhenStatusRunningAllPages() throws MalformedURLException {
        String startTime = "2024-03-11T21:33:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(RUNNING)
                .startTime(startTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn("2");

        PresignedGetObjectRequest presignedGetObjectRequest1 = mock(PresignedGetObjectRequest.class);
        URL url1 = new URL("http://www.url1.com");
        when(presignedGetObjectRequest1.url()).thenReturn(url1);
        when(presignedGetObjectRequest1.expiration()).thenReturn(Instant.now());
        PresignedGetObjectRequest presignedGetObjectRequest2 = mock(PresignedGetObjectRequest.class);
        URL url2 = new URL("http://www.url2.com");
        when(presignedGetObjectRequest2.url()).thenReturn(url2);
        when(presignedGetObjectRequest2.expiration()).thenReturn(Instant.now());

        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest1, presignedGetObjectRequest2);
        when(redisService.isJobInactive(any())).thenReturn(false);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null);

        verify(s3Service, times(2)).getPresignedUrl(any(), any());
        Assert.assertEquals(2, response.getS3Urls().size());
        Assert.assertEquals("http://www.url1.com", response.getS3Urls().get(0).getS3Url());
        Assert.assertEquals("http://www.url2.com", response.getS3Urls().get(1).getS3Url());
    }

    @Test
    public void testHoldingApiSetsS3UrlsWhenStatusRunningSpecificPages() throws MalformedURLException {
        String startTime = "2024-03-11T21:33:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(RUNNING)
                .startTime(startTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn("2");
        PresignedGetObjectRequest presignedGetObjectRequest = fromUrl("url2");
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest);
        when(redisService.isJobInactive(any())).thenReturn(false);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, List.of(2));

        verify(s3Service, times(1)).getPresignedUrl(any(), any());
        Assert.assertEquals(1, response.getS3Urls().size());
        Assert.assertEquals("url2", response.getS3Urls().get(0).getS3Url());
    }

    @Test
    public void getStatusHoldingApiSetS3UrlsSuccessAllPages() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn("2");
        PresignedGetObjectRequest presignedGetObjectRequest1 = fromUrl("url1");
        PresignedGetObjectRequest presignedGetObjectRequest2 = fromUrl("url2");
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest1, presignedGetObjectRequest2);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null);

        Assert.assertEquals(2, response.getS3Urls().size());
        Assert.assertEquals("url1", response.getS3Urls().get(0).getS3Url());
        Assert.assertEquals("url2", response.getS3Urls().get(1).getS3Url());
    }

    @Test
    public void getStatusHoldingApiSetS3UrlsSuccessSpecificPages() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn("2");
        PresignedGetObjectRequest presignedGetObjectRequest = fromUrl("url2");
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, List.of(2));

        Assert.assertEquals(1, response.getS3Urls().size());
        Assert.assertEquals("url2", response.getS3Urls().get(0).getS3Url());
    }

    @Test
    public void getStatusHoldingApiUpdateCacheWhenS3UrlsChangeSuccess() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn("2");
        PresignedGetObjectRequest presignedGetObjectRequest1 = fromUrl("newUrl1");
        PresignedGetObjectRequest presignedGetObjectRequest2 = fromUrl("newUrl2");
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest1, presignedGetObjectRequest2);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null);

        Assert.assertEquals(2, response.getS3Urls().size());
        Assert.assertEquals("newUrl1", response.getS3Urls().get(0).getS3Url());
        Assert.assertEquals("newUrl2", response.getS3Urls().get(1).getS3Url());
    }

    @Test
    public void getStatusHoldingApiSetS3UrlsTotalPagesNoValue() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String jobId = "job1";

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage));
        when(redisService.getTotalPages(any())).thenReturn(null);
        AsyncApiResponseEntity response = asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null);

        Assert.assertNull(response.getS3Urls());
    }

    @Test
    public void getStatusHoldingApiSetS3UrlsException() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String urlExpireTime = "2024-03-11T22:34:58.546";
        String jobId = "job1";
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .urlExpireTime(urlExpireTime)
                .environment(ENVIRONMENT)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();
        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(String.valueOf(AsyncInput.ApiType.HOLDING_API))
                .totalPages(2)
                .build();
        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);
        when(redisService.getRedisKey(any())).thenReturn(JsonUtils.toJsonString(cacheMessage)).thenReturn(null);
        when(redisService.getTotalPages(any())).thenReturn("2").thenReturn(null);
        when(s3Service.getPresignedUrl(any(), any())).thenThrow(new SdkClientException("s3 error"));

        InvestmentApiValidationException exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null)
        );
        Assert.assertEquals(Status.INTERNAL_ERROR, exception.getStatus());

        exception = Assert.assertThrows(InvestmentApiValidationException.class, () ->
                asyncApiService.getPHStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID, null)
        );
        Assert.assertEquals(Status.INTERNAL_ERROR, exception.getStatus());
    }

    @Test
    public void getStatusWithNullApiType() {
        String startTime = "2024-03-11T21:33:58.546";
        String endTime = "2024-03-11T21:34:58.546";
        String jobId = "job1";

        AsyncDbDetails dbOutput = AsyncDbDetails.builder()
                .userId(MOCK_TOKEN_USERID)
                .productId("product1")
                .status(SUCCESS)
                .startTime(startTime)
                .endTime(endTime)
                .environment(ENVIRONMENT)
                .apiType(null)  // Set apiType to null to simulate it not existing in the DB
                .build();

        when(dynamoDao.getAsyncRecord(any())).thenReturn(dbOutput);
        when(redisService.getRedisKey(any())).thenReturn(null);
        PresignedGetObjectRequest presignedGetObjectRequest = fromUrl("url2");
        when(s3Service.getPresignedUrl(any(), any())).thenReturn(presignedGetObjectRequest);// Simulate Redis cache miss

        AsyncApiResponseEntity response = asyncApiService.getGridviewStatus("product1", MOCK_TOKEN, jobId, MOCK_TOKEN_USERID);

        Assert.assertNotNull(response);
        Assert.assertEquals(SUCCESS, response.getJobStatus());
        Assert.assertEquals(startTime, response.getStartTime());
        Assert.assertEquals(endTime, response.getEndTime());
        Assert.assertEquals(AsyncInput.ApiType.INVESTMENT_API, response.getApiType());
    }

    private PresignedGetObjectRequest fromUrl(String url) {
        URL mockUrl = mock(URL.class);
        when(mockUrl.toString()).thenReturn(url);

        PresignedGetObjectRequest presignedGetObjectRequest = mock(PresignedGetObjectRequest.class);
        when(presignedGetObjectRequest.url()).thenReturn(mockUrl);
        when(presignedGetObjectRequest.expiration()).thenReturn(Instant.now());
        return presignedGetObjectRequest;
    }
}
