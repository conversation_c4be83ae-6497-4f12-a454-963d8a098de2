package com.morningstar.martapi.util;

import org.junit.jupiter.api.Test;
import org.springframework.http.server.reactive.ServerHttpRequest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.springframework.http.HttpHeaders;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;


public class CompressionUtilsTest {

    @Test
    public void testIsGzipRequest() {
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Encoding", "gzip");
        when(request.getHeaders()).thenReturn(headers);

        assertTrue(CompressionUtils.isGzipRequest(request));
    }

    @Test
    public void testIsGzipResponseRequired() {
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept-Encoding", "gzip");
        when(request.getHeaders()).thenReturn(headers);

        assertTrue(CompressionUtils.isGzipResponseRequired(request));
    }

    @Test
    public void testGetDeflatedBytes() throws IOException {
        String testData = "Test Data";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(testData.getBytes(StandardCharsets.UTF_8));

        byte[] result = CompressionUtils.getDeflatedBytes(inputStream);
        String resultString = new String(result, StandardCharsets.UTF_8);

        assertEquals(testData, resultString);
    }
}