package com.morningstar.martapi.exception;

import com.morningstar.martapi.validator.entity.HeadersAndParams;
import lombok.Getter;

@Getter
public class TranscriptApiException extends RuntimeException {


    private final HeadersAndParams headersAndParams;
    private final String userId;

    public TranscriptApiException(String message, HeadersAndParams headersAndParams, String userId, Throwable cause) {
        super(message, cause);
        this.headersAndParams = headersAndParams;
        this.userId = userId;
    }

}
