package com.morningstar.martapi.util;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.CurrentPair;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public class InvestmentResponseTest {

    public static InvestmentResponse createCustomInvestmentResponse() {

        CurrentPair currentPair = new CurrentPair("EQOO4", "test_company_id", null, null);
        CurrentPair currentPairDataA = new CurrentPair("EQN3D", UUID.randomUUID().toString(), null, null);
        CurrentPair currentPairDataB = new CurrentPair("EQ56B","2024-05-03T08:30:00Z", null, null);

        Investment investment = new Investment();
        investment.setId("test_performance_id");
        investment.setCurrentPairList(List.of(currentPair,currentPairDataA,currentPairDataB));
        investment.setGroupDataList(List.of());
        investment.setTimeseriesDataList(List.of());
        investment.setErrors(Set.of());

        InvestmentResponse customInvestmentResponse = new InvestmentResponse();
        customInvestmentResponse.setStatus(Status.SUCCESS);
        customInvestmentResponse.setInvestments(List.of(investment));

        return customInvestmentResponse;
    }

    public static InvestmentResponse createCustomInvestmentResponseFailure() {

        CurrentPair currentPair = new CurrentPair("EQOO4", "test_company_id", null, null);


        Investment investment = new Investment();
        investment.setId("test_performance_id");
        investment.setCurrentPairList(List.of(currentPair));
        investment.setGroupDataList(List.of());
        investment.setTimeseriesDataList(List.of());
        investment.setErrors(Set.of());

        InvestmentResponse customInvestmentResponse = new InvestmentResponse();
        customInvestmentResponse.setStatus(Status.SUCCESS);
        customInvestmentResponse.setInvestments(List.of(investment));

        return customInvestmentResponse;
    }

    public static InvestmentResponse createCustomInvestmentResponseFailureCurrentPair() {

        Investment investment = new Investment();
        investment.setId("test_performance_id");
        investment.setGroupDataList(List.of());
        investment.setTimeseriesDataList(List.of());
        investment.setErrors(Set.of());

        InvestmentResponse customInvestmentResponse = new InvestmentResponse();
        customInvestmentResponse.setStatus(Status.SUCCESS);
        customInvestmentResponse.setInvestments(List.of(investment));

        return customInvestmentResponse;
    }


    @Test
    public void checkInvestmentResponse() {
        Assert.assertEquals(Status.SUCCESS, createCustomInvestmentResponse().getStatus());
    }
}
