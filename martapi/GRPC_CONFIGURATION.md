# gRPC 服务配置说明

## 概述

本项目支持通过环境变量控制gRPC服务的启用/禁用。默认情况下，gRPC服务是**完全禁用**的，包括所有gRPC相关的自动配置和对象初始化，确保对现有代码没有任何影响。只有在明确设置环境变量时才会启用gRPC功能。

## 配置方式

### 环境变量

- `ENABLE_GRPC`: 控制是否启用gRPC服务
  - `true`: 启用gRPC服务
  - `false` 或未设置: 禁用gRPC服务（默认）

### 应用配置

在 `application.yml` 中的配置：

```yaml
grpc:
  server:
    enabled: ${ENABLE_GRPC:false}  # 默认为false
    port: -1
    max-inbound-metadata-size: 32768
```

## 使用方法

### 启用gRPC服务

```bash
# Linux/Mac
export ENABLE_GRPC=true
java -jar martapi.jar

# 或者直接在启动时设置
ENABLE_GRPC=true java -jar martapi.jar

# Windows
set ENABLE_GRPC=true
java -jar martapi.jar
```

### 禁用gRPC服务（默认）

```bash
# 不设置环境变量，或者明确设置为false
export ENABLE_GRPC=false
java -jar martapi.jar

# 或者不设置环境变量（默认行为）
java -jar martapi.jar
```

## 影响的组件

当gRPC服务被禁用时（`ENABLE_GRPC=false` 或未设置），以下组件**完全不会被加载或初始化**：

1. **gRPC自动配置类**：
   - `GrpcServerAutoConfiguration`
   - `GrpcServerFactoryAutoConfiguration`
   - `GrpcServerSecurityAutoConfiguration`

2. **自定义gRPC组件**：
   - `TimeSeriesGrpcService` - gRPC时间序列服务
   - `MetadataInterceptor` - gRPC元数据拦截器
   - `GrpcConfig` - gRPC配置类

3. **gRPC相关对象**：
   - gRPC服务器实例
   - gRPC通道和连接
   - Protobuf相关的序列化器
   - 所有gRPC拦截器和过滤器

这确保了当gRPC禁用时，不会有任何gRPC相关的对象被创建或初始化，对现有代码没有任何性能或功能影响。

## 验证方法

### 检查gRPC服务是否启用

1. 查看应用启动日志，如果gRPC启用，会看到类似以下日志：
   ```
   gRPC Server started, listening on address: 0.0.0.0, port: [端口号]
   ```

2. 如果gRPC禁用，不会看到gRPC相关的启动日志

### 测试gRPC服务

当gRPC启用时，可以使用gRPC客户端连接到服务进行测试。

## 技术实现

### 自动配置排除
在 `MartAPIApplication.java` 中排除了所有gRPC相关的自动配置：

```java
@SpringBootApplication(exclude = {
    MongoAutoConfiguration.class,
    GrpcServerAutoConfiguration.class,
    GrpcServerFactoryAutoConfiguration.class,
    GrpcServerSecurityAutoConfiguration.class
})
```

### 条件化导入
通过 `GrpcConfig.java` 只在 `grpc.server.enabled=true` 时才导入gRPC自动配置：

```java
@Configuration
@ConditionalOnProperty(
    name = "grpc.server.enabled",
    havingValue = "true",
    matchIfMissing = false
)
@Import({
    GrpcServerAutoConfiguration.class,
    GrpcServerFactoryAutoConfiguration.class,
    GrpcServerSecurityAutoConfiguration.class
})
public class GrpcConfig {
}
```

## 注意事项

1. **完全隔离**：当 `ENABLE_GRPC=false` 时，不会加载任何gRPC相关的类或对象
2. **零影响**：禁用gRPC不会对现有的REST API或其他功能产生任何影响
3. **资源节省**：避免了不必要的gRPC服务器启动和资源消耗
4. **修改环境变量后需要重启应用才能生效**
5. **默认禁用**：确保向后兼容性，不会意外启用gRPC服务
6. **在生产环境中，建议根据实际需求决定是否启用gRPC服务**
