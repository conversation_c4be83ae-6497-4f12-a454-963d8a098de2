package com.morningstar.martapi.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

@RunWith(MockitoJUnitRunner.class)
public class S3ServiceTest {

    @Mock
    private AmazonS3 s3Client;
    @Mock
    private S3PreSignerProvider s3PreSignerProvider;

    S3Service target;

    @Before
    public void setup() {
        target = new S3Service(s3Client, "inputBucket", "usOutputBucket", "euOutputBucket", "us-east-1", s3PreSignerProvider);
    }

    @Test
    public void testUpload() {
        target.putObject("key.json", "test", new ObjectMetadata());
        Mockito.verify(s3Client, times(1)).putObject(any(PutObjectRequest.class));
    }

    @Test
    public void testGetPresignedUrl() {
        S3Presigner s3Presigner = Mockito.mock(S3Presigner.class);
        when(s3Presigner.presignGetObject(any(GetObjectPresignRequest.class))).thenReturn(null);
        when(s3PreSignerProvider.getS3Presigner("us-east-1")).thenReturn(s3Presigner);
        target.getPresignedUrl("jobId", null);
        Mockito.verify(s3Presigner, times(1)).presignGetObject(any(GetObjectPresignRequest.class));
    }

    @Test
    public void getEuBucketTest() {
        S3Presigner s3Presigner = Mockito.mock(S3Presigner.class);
        when(s3Presigner.presignGetObject(any(GetObjectPresignRequest.class))).thenReturn(null);
        when(s3PreSignerProvider.getS3Presigner("eu-west-1")).thenReturn(s3Presigner);
        target.getPresignedUrl("jobId", "eu-west-1");
        Mockito.verify(s3Presigner, times(1)).presignGetObject(any(GetObjectPresignRequest.class));
    }
}
