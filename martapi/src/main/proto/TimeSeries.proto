syntax = "proto3";

package protobuf;
import "TsCacheData.proto";

option java_multiple_files = true;
option java_package = "com.morningstar.martapi.grpc.proto";
option java_outer_classname = "TimeSeriesProtoBuf";

service TimeSeriesService {
  rpc RetrieveTimeSeriesData(TimeSeriesRequest) returns (TimeSeriesDatas);
}


message TimeSeriesRequest {
  repeated string investmentIds = 1;
  repeated string dataPoints = 2;
  string startDate = 3;
  string endDate = 4;
  string currency = 5;
  string preCurrency = 6;
  string dateFormat = 7;
  string decimalFormat = 8;
  string extendPerformance = 9;
  string postTax = 10;
  bool useRequireId = 11;
  string useCase = 12;
  bool useNewCcs = 13;
  string entitlementProductId = 14;
}