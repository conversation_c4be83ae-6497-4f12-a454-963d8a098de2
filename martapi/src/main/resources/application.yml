spring:
  profiles:
    active: dev
  jackson:
    default-property-inclusion: non_null
  codec:
    max-in-memory-size: 10MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
      web-application-type: reactive

grpc:
  server:
    port: -1
    max-inbound-metadata-size: 32768

logging:
  level:
    com: INFO
    org: INFO
    reactor: ERROR

server:
  compression:
    enabled: true
    min-response-size: 2048
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml,application/x-protobuf

authentication:
  jwt:
    issuer: https://login-stg.morningstar.com
---
spring:
  profiles: dev

aws:
  region: us-east-1
  s3:
    inputBucket: async-mart-api-input-dev
    outputBucket:
      us: async-mart-api-output-dev
      eu: async-mart-api-output-dev-eu
  lambda:
    function: asyncapi-scaling-lambda-dev
  dynamoDb:
    table: async-martapi-history-dev
martcommon:
  aws:
    s3:
      mode: dynamic # Mart api dev will be using b/g
      default-env: blue
  localCache:
    enableCache: true

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 180000
    connectionTimeout: 30000
    maximumPoolSize: 20
    minimumIdle: 20
    idleTimeout: 60000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 5
    minimumIdle: 0
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: true
    maximumPoolSize: 5
    minimumIdle: 0

topic:
  sync: config_update_dev
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-stg-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

---
spring:
  profiles: dev-eu

topic:
  sync: config_update_dev
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: eu-west-1
  s3:
    inputBucket: async-mart-api-input-dev-eu
    outputBucket:
      us: async-mart-api-output-dev
      eu: async-mart-api-output-dev-eu
  lambda:
    function: asyncapi-scaling-lambda-dev
  dynamoDb:
    table: async-martapi-history-dev

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 570000
    connectionTimeout: 30000
    maximumPoolSize: 20
    minimumIdle: 20
    idleTimeout: 100000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 5
    minimumIdle: 0
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 5
    minimumIdle: 0

martcommon:
  localCache:
    enableCache: true

asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-stg-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

---
spring:
  profiles: qa

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 180000
    connectionTimeout: 30000
    maximumPoolSize: 100
    minimumIdle: 20
    idleTimeout: 60000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 10
    minimumIdle: 5
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 10
    minimumIdle: 5

martcommon:
  localCache:
    enableCache: true
topic:
  sync: config_update_stg
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: us-east-1
  s3:
    inputBucket: async-mart-api-input-qa
    outputBucket:
      us: async-mart-api-output-qa
      eu: async-mart-api-output-qa
  lambda:
    function: asyncapi-scaling-lambda-qa
  dynamoDb:
    table: async-martapi-history-qa
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-stg-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

---
spring:
  profiles: stg

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 180000
    connectionTimeout: 30000
    maximumPoolSize: 100
    minimumIdle: 20
    idleTimeout: 60000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 10
    minimumIdle: 5
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 10
    minimumIdle: 5

topic:
  sync: config_update_stg
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: us-east-1
  s3:
    inputBucket: async-mart-api-input-stg
    outputBucket:
      us: async-mart-api-output-stg
      eu: async-mart-api-output-stg-eu
  lambda:
    function: asyncapi-scaling-lambda-stg
  dynamoDb:
    table: async-martapi-history-stg
martcommon:
  aws:
    s3:
      mode: dynamic # Mart api stg will be using b/g
      default-env: blue
  localCache:
    enableCache: true
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-stg-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

---
spring:
  profiles: stg-eu

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 180000
    connectionTimeout: 30000
    maximumPoolSize: 100
    minimumIdle: 20
    idleTimeout: 60000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 10
    minimumIdle: 5
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 10
    minimumIdle: 5

topic:
  sync: config_update_stg
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: eu-west-1
  s3:
    inputBucket: async-mart-api-input-stg-eu
    outputBucket:
      us: async-mart-api-output-stg
      eu: async-mart-api-output-stg-eu
  lambda:
    function: asyncapi-scaling-lambda-stg
  dynamoDb:
    table: async-martapi-history-stg
martcommon:
  aws:
    s3:
      mode: dynamic
      default-env: blue
  localCache:
    enableCache: true
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-stg-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

---

spring:
  profiles: prod

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 570000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 40
    idleTimeout: 100000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 10
    minimumIdle: 5
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 10
    minimumIdle: 5

topic:
  sync: config_update_prod
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: us-east-1
  s3:
    inputBucket: async-mart-api-input-prod
    outputBucket:
      us: async-mart-api-output-prod
      eu: async-mart-api-output-prod-eu
  lambda:
    function: asyncapi-scaling-lambda-prod
  dynamoDb:
    table: async-martapi-history-prod
martcommon:
  aws:
    s3:
      mode: dynamic # Mart api prod will be using b/g
      default-env: blue
  localCache:
    enableCache: true
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

authentication:
  jwt:
    issuer: https://login-prod.morningstar.com

---

spring:
  profiles: prod-eu

martgateway:
  version: @mart-gateway.version@
  rdb:
    maxLifetime: 570000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 20
    idleTimeout: 100000
  redshift:
    password: /LAKEHOUSE/ADMIN_PASSWORD
  martapi: true
  application: martapi
  eod:
    maximumPoolSize: 10
    minimumIdle: 5
  rds:
    maximumPoolSize: 10
    minimumIdle: 5
  modules:
    fixedincome:
      enable: true
    leinace:
      enable: true
  custom-data:
    enabled: false
    maximumPoolSize: 10
    minimumIdle: 5

topic:
  sync: config_update_prod
  localIdMap: local_id_cache_update
  entitlement: entitlement_cache_update
  uimTokenClearCache: uim_token_clear_cache
aws:
  region: eu-west-1
  s3:
    inputBucket: async-mart-api-input-prod-eu
    outputBucket:
      us: async-mart-api-output-prod
      eu: async-mart-api-output-prod-eu
  lambda:
    function: asyncapi-scaling-lambda-prod
  dynamoDb:
    table: async-martapi-history-prod
martcommon:
  aws:
    s3:
      mode: dynamic
      default-env: blue
  localCache:
    enableCache: true
asyncApi:
  threadPoolSize: 20
transcript:
  url: "https://www.us-api.morningstar.com/dc-gpt-public/v1/speech-processing/job-result"

authentication:
  jwt:
    issuer: https://login-prod.morningstar.com
