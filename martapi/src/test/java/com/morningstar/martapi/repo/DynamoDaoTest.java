package com.morningstar.martapi.repo;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.async.entity.AsyncDbDetails;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;

@RunWith(MockitoJUnitRunner.class)
public class DynamoDaoTest {

    DynamoDao dynamoDao;
    DynamoDbTable dynamoDbTable;

    @Before
    public void setup() {
        dynamoDbTable = Mockito.mock(DynamoDbTable.class);
        dynamoDao = new DynamoDao(dynamoDbTable);

    }

    @Test
    public void shouldSave() {
        dynamoDao.upsertAsyncRecord(new AsyncDbDetails());
        verify(dynamoDbTable, times(1)).putItem(any(PutItemEnhancedRequest.class));
    }

    @Test
    public void shouldGet() {
        AsyncDbDetails asyncDbDetails = new AsyncDbDetails();
        when(dynamoDbTable.getItem(any(Key.class))).thenReturn(asyncDbDetails);
        AsyncDbDetails target = dynamoDao.getAsyncRecord("job1");
        Assert.assertEquals(asyncDbDetails, target);
    }

}
