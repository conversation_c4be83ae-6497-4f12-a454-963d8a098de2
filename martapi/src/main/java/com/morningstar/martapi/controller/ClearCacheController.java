package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.martapi.service.RedisMessagePublisher;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import springfox.documentation.annotations.ApiIgnore;

import javax.inject.Inject;
import java.util.UUID;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@RestController
@RequestMapping(value = {"/v1/clear-cache"}, produces = {MediaType.APPLICATION_JSON_VALUE})
@ApiIgnore
public class ClearCacheController {

    private static final Logger log = LoggerFactory.getLogger(ClearCacheController.class);
    private final RequestValidationHandler<HeadersAndParams, ClearCacheRequest> validator;
    private final MartGateway<MartResponse, ClearCacheRequest> clearCacheGateway;
    private RedisMessagePublisher redisMessagePublisher;

    @Inject
    public ClearCacheController(
            MartGateway<MartResponse, ClearCacheRequest> clearCacheGateway,
            @Qualifier("clearCacheValidator") RequestValidationHandler<HeadersAndParams, ClearCacheRequest> validator,
            RedisMessagePublisher redisMessagePublisher) {
        this.validator = validator;
        this.clearCacheGateway = clearCacheGateway;
        this.redisMessagePublisher = redisMessagePublisher;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<MartResponse> clearCache(
            @RequestBody ClearCacheRequest clearCacheRequest,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName,
            ServerHttpRequest request)
    {
        Long startTime = System.currentTimeMillis();
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())
                .serviceName(serviceName)
                .tsId(tsId)
                .build();
        validateRequest(clearCacheRequest, headersAndParams);
        String userIdFromToken = JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
        clearCacheRequest.setUserId(userIdFromToken);
        clearCacheRequest.setRequestId(requestId);

        return clearCacheGateway.asyncRetrieveSecurities(clearCacheRequest).doOnEach(LogHelper.logOnNext(list -> {
             LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                     new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                     new LogEntity(PRODUCT_ID, productId),
                     new LogEntity(SERVICE_NAME_CALLER, serviceName),
                     new LogEntity(TS_ID, tsId),
                     new LogEntity(USER_ID, headerUserId),
                     new LogEntity(REQUEST_PARAM, request.getURI()),
                     new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
         })).doOnEach(LogHelper.logOnError(e -> {
             LogEntry.error(
                     new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                     new LogEntity(EVENT_DESCRIPTION, e),
                     new LogEntity(PRODUCT_ID, productId),
                     new LogEntity(SERVICE_NAME_CALLER, serviceName),
                     new LogEntity(TS_ID, tsId),
                     new LogEntity(USER_ID, headerUserId),
                     new LogEntity(REQUEST_PARAM, request.getURI()),
                     new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
         }));
    }

    @DeleteMapping(value = "uim-token")
    public ResponseEntity<Void> clearUimCache (
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName) {
        if (!"mds".equalsIgnoreCase(productId)) { // only allow mds
            throw new ValidationException(Status.INVALID_PRODUCT_ID);
        }
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())
                .serviceName(serviceName)
                .tsId(tsId)
                .build();
        validator.validateHeadersAndParams(headersAndParams);
        String userIdFromToken = JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
        log.info("Received request to clear UIM token cache from {}", userIdFromToken);
        try {
            redisMessagePublisher.publishUimTokenClearCache("Clear UIM Token Cache");
            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(USER_ID, headerUserId));
            return ResponseEntity.noContent().build();
        } catch (Exception e) {

            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, String.format("\"Clear Cache\", event_description=\"Clear UIM Token Cache Error\", error_message=\"%s\"", e.getMessage())),
                    new LogEntity(EXCEPTION_MESSAGE, e),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId));
            return ResponseEntity.internalServerError().build();
        }
    }

    private void validateRequest(ClearCacheRequest clearCacheRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(clearCacheRequest);
    }
}