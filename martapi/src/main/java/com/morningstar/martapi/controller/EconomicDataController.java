package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataResult;
import io.swagger.annotations.ApiParam;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@RestController
@RequestMapping(value = {"/v1/economic-data/","/investment-api/v1/economic-data/"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class EconomicDataController {

    private final MartGateway<EconomicDataResult, EconomicDataRequest> economicDataGateway;
    private final RequestValidationHandler<HeadersAndParams, EconomicDataRequest> validator;

    private static final String REQUEST_TYPE_VALUE = "economic-data";

    public EconomicDataController(@Qualifier("economicDataGatewayImpl") MartGateway<EconomicDataResult, EconomicDataRequest> economicDataGateway, @Qualifier("economicDataValidator") RequestValidationHandler<HeadersAndParams, EconomicDataRequest> validator) {
        this.validator = validator;
        this.economicDataGateway = economicDataGateway;
    }

    @GetMapping(value = "/indicator-id/{seriesId}")
    public Mono<EconomicDataResult> getEconomicData(
            @ApiParam(example = "0P00000AWG,0P00000AYI,0P00000AZ0")
            @PathVariable("seriesId") String seriesId,
            @ApiParam(example = "2000-12-31")
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2020-11-30")
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName
    ) {
        HeadersAndParams headersAndParams = new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId, tsId, serviceName);
        validator.validateHeadersAndParams(headersAndParams);

        EconomicDataRequest economicDataRequest = EconomicDataRequest.builder()
                .fredSeriesId(seriesId)
                .startDate(startDate)
                .endDate(endDate)
                .build();

        validator.validateRequestBody(economicDataRequest);

        return Mono.defer(() -> {
            long startTime = System.currentTimeMillis();
            return economicDataGateway.asyncRetrieveSecurities(economicDataRequest).doOnEach(LogHelper.logOnNext(list -> {
                List<LogEntity> logEntities = logAccess(economicDataRequest, startTime, requestId, tsId, serviceName);
                LogEntry.info(logEntities.toArray(LogEntity[]::new));
            })).doOnEach(LogHelper.logOnError(e -> {
                List<LogEntity> logEntities = logError(economicDataRequest, startTime, requestId, tsId, serviceName, e);
                LogEntry.error(logEntities.toArray(LogEntity[]::new));
            }));
        });
    }

    private List<LogEntity> logAccess(
            EconomicDataRequest economicDataRequest,
            long startTime,
            String requestId,
            String tsId,
            String serviceName
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        long executionTime = System.currentTimeMillis() - startTime;
        return Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(economicDataRequest)),
                new LogEntity(EXECUTE_TIME, executionTime),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId)
        ).collect(Collectors.toCollection(ArrayList::new));
    }

    private List<LogEntity> logError(
            EconomicDataRequest economicDataRequest,
            long startTime,
            String requestId,
            String tsId,
            String serviceName,
            Throwable e
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        return Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, e),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(economicDataRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, e.getClass()),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId)
        ).collect(Collectors.toCollection(ArrayList::new));
    }
}
