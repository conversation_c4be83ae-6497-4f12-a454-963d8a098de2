package com.morningstar.martapi.util;

import com.morningstar.dataac.martgateway.core.common.util.JwtUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TokenUtil {
    public static String getUserId(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
    }
    public static  String getConfigId(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
    }
}
