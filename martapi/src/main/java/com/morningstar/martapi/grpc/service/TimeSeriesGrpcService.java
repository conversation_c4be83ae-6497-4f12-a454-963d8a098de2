package com.morningstar.martapi.grpc.service;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.core.common.util.JwtUtil;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.grpc.interceptor.MetadataInterceptor;
import com.morningstar.martapi.grpc.entity.GrpcMetadata;
import com.morningstar.martapi.grpc.proto.TimeSeriesRequest;
import com.morningstar.martapi.grpc.proto.TimeSeriesServiceGrpc;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import io.grpc.Metadata;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;
import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@GrpcService
@ConditionalOnProperty(
    name = "grpc.server.enabled",
    havingValue = "true",
    matchIfMissing = false
)
public class TimeSeriesGrpcService extends TimeSeriesServiceGrpc.TimeSeriesServiceImplBase {

    private final MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private final RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    private static final String REQUEST_TYPE_VALUE = "timeseries_grpc";

    private static final Metadata.Key<String> AUTHORIZATION_KEY =
            Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> PRODUCT_ID_KEY =
            Metadata.Key.of("product-id", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> REQUEST_ID_KEY =
            Metadata.Key.of("request-id", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> ENTITLEMENT_CHECK_KEY =
            Metadata.Key.of("entitlement-check", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> READ_CACHE_KEY =
            Metadata.Key.of("read-cache", Metadata.ASCII_STRING_MARSHALLER);

    public TimeSeriesGrpcService(MartGateway<TSResponse, MartRequest> tsOldRspGateway,
                                 @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams, MartRequest> validator) {
        this.tsOldRspGateway = tsOldRspGateway;
        this.validator = validator;
    }

    @Override
    public void retrieveTimeSeriesData(TimeSeriesRequest request, StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver) {
        long startTime = System.currentTimeMillis();
        Metadata metadata = MetadataInterceptor.METADATA_KEY.get();
        MartRequest martRequest = convertToMartRequest(request, extractGrpcMetadata(metadata));

        try {
            HeadersAndParams headersAndParams = convertToHeadersAndParams(extractGrpcMetadata(metadata));
            validateRequest(martRequest, headersAndParams);
            handleAsyncRetrieveSecurities(martRequest, startTime, responseObserver, TSResponse::toProtobuf);
        } catch (Exception e) {
            handleException(e, martRequest, startTime, responseObserver);
        }
    }

    private MartRequest convertToMartRequest(TimeSeriesRequest grpcRequest, GrpcMetadata grpcMetadata) {
        String userId = getUserIdFromToken(grpcMetadata.getAuthorization());
        String configId = getConfigIdFromToken(grpcMetadata.getAuthorization());

        List<String> investmentIds = grpcRequest.getInvestmentIdsList();
        List<String> dataPoints = grpcRequest.getDataPointsList();

        return MartRequest.builder()
                .currency(grpcRequest.getCurrency())
                .dps(dataPoints)
                .ids(investmentIds)
                .startDate(grpcRequest.getStartDate())
                .endDate(grpcRequest.getEndDate())
                .preCurrency(grpcRequest.getPreCurrency())
                .readCache(grpcMetadata.getReadCache())
                .productId(grpcMetadata.getProductId())
                .entitlementProductId(grpcRequest.getEntitlementProductId())
                .requestId(grpcMetadata.getRequestId())
                .userId(userId)
                .dateFormat(grpcRequest.getDateFormat())
                .decimalFormat(grpcRequest.getDecimalFormat())
                .extendedPerformance(grpcRequest.getExtendPerformance())
                .postTax(grpcRequest.getPostTax())
                .useRequireId(grpcRequest.getUseRequireId())
                .checkEntitlement(grpcMetadata.isEntitlementCheck())
                .useCase(grpcRequest.getUseCase())
                .useNewCCS(grpcRequest.getUseNewCcs())
                .configId(configId)
                .build();
    }

    private HeadersAndParams convertToHeadersAndParams(GrpcMetadata grpcMetadata) {
        return HeadersAndParams.builder()
                .authorizationToken(grpcMetadata.getAuthorization())
                .productId(grpcMetadata.getProductId())
                .requestId(grpcMetadata.getRequestId())
                .build();
    }

    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        try {
            validator.validateHeadersAndParams(headersAndParams);
            validator.validateRequestBody(martRequest);
        } catch (ValidationException e) {
            throw new TsCacheProtobufValidationException(e.getStatus());
        }
    }

    private static String getUserIdFromToken(String token) {

        return JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");

    }

    private static String getConfigIdFromToken(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
    }

    private String extractFromMetadata(Metadata metadata, Metadata.Key<String> key) {
        if (metadata == null) {
            return null;
        }
        return metadata.get(key);
    }

    private GrpcMetadata extractGrpcMetadata(Metadata metadata) {
        String authorization = extractFromMetadata(metadata, AUTHORIZATION_KEY);
        String productId = extractFromMetadata(metadata, PRODUCT_ID_KEY);
        String requestId = extractFromMetadata(metadata, REQUEST_ID_KEY);

        String entitlementCheckStr = extractFromMetadata(metadata, ENTITLEMENT_CHECK_KEY);
        if (entitlementCheckStr == null) {
            entitlementCheckStr = "true";
        }
        boolean entitlementCheck = "true".equalsIgnoreCase(entitlementCheckStr);

        String readCacheStr = extractFromMetadata(metadata, READ_CACHE_KEY);

        return GrpcMetadata.builder()
                .authorization(authorization)
                .productId(productId)
                .requestId(requestId)
                .entitlementCheck(entitlementCheck)
                .readCache(readCacheStr)
                .build();
    }

    private void logSuccess(MartRequest martRequest, long startTime) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        long executionTime = System.currentTimeMillis() - startTime;
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId()),
                new LogEntity(EXECUTE_TIME, executionTime)
        ).collect(Collectors.toCollection(ArrayList::new));
        addRequestPayload(martRequest, executionTime, logEntities);
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    private void logError(MartRequest martRequest, long startTime, Throwable error) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        LogEntry.error(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, error),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, error.getClass()),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId())
        );
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List<LogEntity> logEntities) {
        if (executionTime > LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
    }

    private <T> void handleAsyncRetrieveSecurities(MartRequest martRequest, long startTime, StreamObserver<T> responseObserver, Function<TSResponse, T> responseConverter) {
        tsOldRspGateway.asyncRetrieveSecurities(martRequest)
                .subscribe(
                        result -> {
                            T response = responseConverter.apply(result);
                            responseObserver.onNext(response);
                            responseObserver.onCompleted();
                            logSuccess(martRequest, startTime);
                        },
                        error -> {
                            responseObserver.onError(error);
                            logError(martRequest, startTime, error);
                        }
                );
    }

    private void handleException(Exception e, MartRequest martRequest, long startTime, StreamObserver<?> responseObserver) {
        logError(martRequest, startTime, e);
        io.grpc.Status status;
        if (e instanceof TsCacheProtobufValidationException) {
            status = io.grpc.Status.INVALID_ARGUMENT.withDescription(e.getMessage());
        } else if (e instanceof ValidationException) {
            status = io.grpc.Status.INVALID_ARGUMENT.withDescription(e.getMessage());
        } else {
            status = io.grpc.Status.INTERNAL.withDescription(e.getMessage());
        }
        responseObserver.onError(status.asRuntimeException());
    }
}
