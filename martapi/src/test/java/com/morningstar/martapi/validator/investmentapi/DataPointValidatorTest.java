package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martequity.validation.EquityRequestValidation;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;
import java.util.List;

import static org.mockito.Mockito.mock;

public class DataPointValidatorTest {

    private DataPointValidator validator;

    @Before
    public void setup() {
        this.validator = new DataPointValidator();
    }

    @Test
    public void validate() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());

        InvestmentApiRequest request2 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .build();
        exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request2));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());

        InvestmentApiRequest request3 = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(List.of(GridviewDataPoint.builder().build(), GridviewDataPoint.builder().build()))
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .build();
        exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request3));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateRequestBodyDuplicateAlias() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").alias("Z1").build(), GridviewDataPoint.builder().dataPointId("HP022").alias("Z1").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.DUPLICATE_DATAPOINT.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateRequestBodyBlankAlias() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").alias("  ").build(), GridviewDataPoint.builder().dataPointId("HP022").alias("Z1").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.BLANK_ALIAS.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateRequestBodyDuplicateDataPoint() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").frequency("m").build(), GridviewDataPoint.builder().dataPointId("HP010").frequency("m").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.DUPLICATE_DATAPOINT.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateEquityDataPoint() throws NoSuchFieldException, IllegalAccessException {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01011").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointIds(List.of("EQB94","1111","2222")).build()))
                .build();

        DataPointValidator dataPointValidator = new DataPointValidator();

        EquityRequestValidation mockedValidate = mock(EquityRequestValidation.class);
        Field validateField = DataPointValidator.class.getDeclaredField("equityRequestValidation");
        validateField.set(dataPointValidator, mockedValidate);

        when(mockedValidate.validateDataPointRequest(any())).thenReturn(List.of("EQB94"));

        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> dataPointValidator.validate(request1));
        Assertions.assertEquals(Status.INVALID_DATAPOINT.getCode(),exception.getStatus().getCode());
        Assertions.assertEquals("Invalid datapoint parameters for datapointIds: EQB94",exception.getMessage());
    }
    @Test
    public void validateGridviewDatapointWithEitherDatapointIdsOrDatapointId() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("0P000000049").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("EQ000").dataPointIds(List.of("EQ001","EQ002")).build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATAPOINT_REQUEST_FORMAT.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void testValidate_DataPointIdConflictsWithExistingAlias() {
        // First datapoint uses alias that will conflict with another datapoint's ID
        GridviewDataPoint dp1 = GridviewDataPoint.builder()
                .dataPointId("DP1")
                .alias("CONFLICT_ID")
                .build();

        // Second datapoint uses ID matching first datapoint's alias
        GridviewDataPoint dp2 = GridviewDataPoint.builder()
                .dataPointId("CONFLICT_ID")
                .alias("ALIAS2")
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("INV1").build()))
                .dataPoints(List.of(dp1, dp2))
                .build();

        Assertions.assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_DataPointIdSameAsPreviousAlias() {
        // Direct test for the provided example case
        GridviewDataPoint dp1 = GridviewDataPoint.builder()
                .dataPointId("OS466")
                .alias("OS466")
                .build();

        GridviewDataPoint dp2 = GridviewDataPoint.builder()
                .dataPointId("OS466")
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("INV1").build()))
                .dataPoints(List.of(dp1, dp2))
                .build();

        InvestmentApiValidationException exception = Assertions.assertThrows(
                InvestmentApiValidationException.class,
                () -> validator.validate(request)
        );

        Assertions.assertEquals(Status.DUPLICATE_DATAPOINT.getCode(), exception.getStatus().getCode());
        Assertions.assertEquals("Data point ID 'OS466' conflicts with existing identifier", exception.getMessage());
    }

    @Test
    public void testValidate_AliasSameAsPreviousDataPointId() {
        // Direct test for the provided example case
        GridviewDataPoint dp1 = GridviewDataPoint.builder()
                .dataPointId("OS466")
                .build();

        GridviewDataPoint dp2 = GridviewDataPoint.builder()
                .dataPointId("OS444")
                .alias("OS466")
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("INV1").build()))
                .dataPoints(List.of(dp1, dp2))
                .build();

        InvestmentApiValidationException exception = Assertions.assertThrows(
                InvestmentApiValidationException.class,
                () -> validator.validate(request)
        );

        Assertions.assertEquals(Status.DUPLICATE_DATAPOINT.getCode(), exception.getStatus().getCode());
        Assertions.assertEquals("Alias 'OS466' conflicts with existing identifier", exception.getMessage());
    }

    @Test
    public void testValidate_EquityDataPointsWithConflictingIds_NoConflict() throws Exception {
        // Equity datapoints with dataPointIds only (no dataPointId)
        GridviewDataPoint dp1 = GridviewDataPoint.builder()
                .dataPointIds(List.of("EQ001"))
                .build();
        GridviewDataPoint dp2 = GridviewDataPoint.builder()
                .dataPointIds(List.of("EQ002"))
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("INV1").build()))
                .dataPoints(List.of(dp1, dp2))
                .build();

        DataPointValidator validator = new DataPointValidator();
        EquityRequestValidation mockValidation = mock(EquityRequestValidation.class);
        when(mockValidation.validateDataPointRequest(any())).thenReturn(List.of());

        Field field = DataPointValidator.class.getDeclaredField("equityRequestValidation");
        field.setAccessible(true);
        field.set(validator, mockValidation);

        Assertions.assertDoesNotThrow(() -> validator.validate(request));
    }
}