package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.contentsevice.common.util.timeperiod.TimeSerialsCalculator;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.contentsevice.common.models.DataPointForColumns;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;


import static com.morningstar.martapi.config.Constant.EXCLUDED_PRODUCT_IDS;
public class ColumnLimitValidator implements Validator<InvestmentApiRequest>  {
    private EquityDatapointUtil equityDatapointUtil;
    private static final int DEFAULT_MAX_COLUMN_LIMIT = 89_421;

    private final int MAX_COLUMN_LIMIT;

    public ColumnLimitValidator(EquityDatapointUtil equityDatapointUtil) {
        MAX_COLUMN_LIMIT = DEFAULT_MAX_COLUMN_LIMIT;
        this.equityDatapointUtil = equityDatapointUtil;
    }

    public ColumnLimitValidator(int maxColumnLimit,EquityDatapointUtil equityDatapointUtil) {
        MAX_COLUMN_LIMIT = maxColumnLimit;
        this.equityDatapointUtil = equityDatapointUtil;
    }

    @Override
    public void validate(InvestmentApiRequest request) throws RuntimeException {
        List<DataPointForColumns> dataPointForColumnsList = new ArrayList<>(request.getDataPoints().size());
        for (GridviewDataPoint dataPoint : request.getDataPoints()) {
            // filter out id that do not need validation.
            if (!shouldValidateDataPoint(dataPoint)) {
                continue;
            }
            dataPointForColumnsList.addAll(buildDataPointForColumns(dataPoint));
        }
        int numberOfColumns = request.getDataPoints().size();
        try { // try to calculate the number of columns with content service api.
            numberOfColumns = CollectionUtils.isNotEmpty(dataPointForColumnsList) ? TimeSerialsCalculator.getNumberOfColumns(dataPointForColumnsList) : numberOfColumns; // need to add preventive step, the number here is not accurate

        } catch (Exception ignored) {
            // ignore all other types of exceptions, if internal error occurs, we will just use the datapoint list size as number of columns.
        }
        // lastly, finish comparison with the actual number of columns.
        if (numberOfColumns > MAX_COLUMN_LIMIT && !isExcludedProductId(request.getProductId())) {
            Status status = Status.BAD_REQUEST.withMessage(
                    String.format("Request input exceeds maximum column limit of [%d] columns. Found [%d] columns "
                                    + "in the request. Please reduce the number of columns.",
                            MAX_COLUMN_LIMIT, numberOfColumns));
            throw new InvestmentApiValidationException(status); // the only error we want to throw here, otherwise ignore
        }
    }

    private List<DataPointForColumns> buildDataPointForColumns(GridviewDataPoint dataPoint) {
        List<DataPointForColumns> dataPointForColumnsList = new ArrayList<>();
        List<String> dataPointList = getAllDatapointIds(dataPoint);
        Set<String> equityDatapointList = dataPointList.stream().filter(equityDatapointUtil::isEquityDatapoint).collect(Collectors.toSet());
        for (String dp : dataPointList ){
            DataPoint dpObj = DataPointRepository.getByNid(dp);
            String frequency = getDatapointFrequency(dpObj, dataPoint.getFrequency());
            DataPointForColumns dataPointForColumns = DataPointForColumns.builder()
                    .datapointId(dp)
                    .alias(dp)
                    .startDate(dataPoint.getStartDate())
                    .endDate(dataPoint.getEndDate())
                    .frequency(frequency)
                    .startDelay(0)
                    .endDelay(0)
                    .windowSize(tryValueOf(dataPoint.getWindowSize()))
                    .windowType(dataPoint.getWindowType())
                    .stepSize(tryValueOf(dataPoint.getStepSize()))
                    .sourceId(dataPoint.getSourceId())
                    .build();
            if(equityDatapointList.contains(dp)){
                dataPointForColumns.setParameterValueCounts(equityDatapointUtil.getParameterValueCount(dp,dataPoint));
            }
            dataPointForColumnsList.add(dataPointForColumns);
        }

        return dataPointForColumnsList;
    }

    private boolean shouldValidateDataPoint(GridviewDataPoint dataPoint) {
        // filter out equity dps (dps without data point id)
        if (StringUtils.isEmpty(dataPoint.getDataPointId()) && CollectionUtils.isEmpty(dataPoint.getDataPointIds())) {
            return false;
        }
        // filter out TS dps without frequency and without either start-date or end-date)
        if (!StringUtils.isEmpty(dataPoint.getStartDate()) || !StringUtils.isEmpty(dataPoint.getEndDate())) {
            return !StringUtils.isEmpty(dataPoint.getFrequency()) && (!StringUtils.isEmpty(dataPoint.getStartDate()) && !StringUtils.isEmpty(dataPoint.getEndDate()));
        }

        return true;
    }

    private Integer tryValueOf(String value) {
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private List<String> getAllDatapointIds(GridviewDataPoint gridviewDataPoint) {
        List<String> gvList = Optional.ofNullable(gridviewDataPoint.getDataPointIds()).isPresent() ? gridviewDataPoint.getDataPointIds() : new ArrayList<>();
        boolean isGvDatapointPresent = Optional.ofNullable(gridviewDataPoint.getDataPointId()).isPresent();
        if (isGvDatapointPresent) {
            gvList.add(gridviewDataPoint.getDataPointId());
        }
        return gvList.stream().filter(Predicate.not(equityDatapointUtil::isMostRecentEquityDatapoint)).toList();
    }

    private boolean isExcludedProductId(String productId){
        return StringUtils.isNotEmpty(productId) && EXCLUDED_PRODUCT_IDS.contains(productId.toUpperCase());
    }

    private String getDatapointFrequency(DataPoint dp,String requestFrequency){
        // This function is used to extract the equity datapoint frequency.
        // If the frequency is empty, it proceeds with the frequency value received through the request.
        boolean check = Optional.ofNullable(dp.getEquityDatapoint()).isPresent();
        String freq = check ? dp.getEquityDatapoint().getTimeSeriesCollection().getFrequency() : StringUtils.EMPTY;
        return StringUtils.isEmpty(freq) ? requestFrequency : freq;
    }

}
