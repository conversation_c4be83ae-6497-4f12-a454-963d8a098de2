package com.morningstar.martapi.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EconomicDataControllerTest {

    private MartGateway<EconomicDataResult, EconomicDataRequest> economicDataGateway;
    private EconomicDataController economicDataController;

    @Before
    public void setup() {
        economicDataGateway = mock(MartGateway.class);
        economicDataController = new EconomicDataController(economicDataGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void getEconomicData() {
        when(economicDataGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(new EconomicDataResult()));
        Mono<EconomicDataResult> economicDataResultMono = economicDataController.getEconomicData("DEXSIUS", "2000-01-01", "2024-01-01", "DIRECT", UUID.randomUUID().toString(), createToken(), "", "");
        StepVerifier.create(economicDataResultMono)
                .expectSubscription()
                .assertNext(r -> Assert.assertEquals(r.getClass(), EconomicDataResult.class)).verifyComplete();
    }


    @Test
    public void getEconomicDataFail() {
        when(economicDataGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<EconomicDataResult> economicDataResultMono = economicDataController.getEconomicData("DEXSIUS", "2000-01-01", "2024-01-01", "DIRECT", UUID.randomUUID().toString(), createToken(), "", "");
        economicDataResultMono.subscribe();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }
}
