package com.morningstar.martapi.validator.economicdata;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class EconomicDataDateValidatorTest {

    private EconomicDataDateValidator validator;

    @BeforeEach
    public void setup() {
        this.validator = new EconomicDataDateValidator();
    }

    @Test
    public void endDateBeforeStartDate() {
        EconomicDataRequest request = EconomicDataRequest.builder()
                .fredSeriesId("DEXSIUS")
                .startDate("2025-01-01")
                .endDate("2024-01-01")
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void validateWithoutDates() {
        EconomicDataRequest request = EconomicDataRequest.builder()
                .fredSeriesId("DEXSIUS")
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void validateWithDates() {
        EconomicDataRequest request = EconomicDataRequest.builder()
                .fredSeriesId("DEXSIUS")
                .startDate("2000-01-01")
                .endDate("2024-01-01")
                .build();
        validator.validate(request);
    }
}
