package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.investmentapi.UseCaseValidator;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

public class TsMartRequestUseCaseValidatorTest {
	private UseCaseValidator validator;

	@Before
	public void setup() {
		this.validator = new UseCaseValidator<>(ValidationException::new);
	}

	@Test
	public void validate() {
		MartRequest request = MartRequest.builder()
				.useCase("unknown")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.INVALID_USE_CASE.getCode(),exception.getStatus().getCode());
	}
}
