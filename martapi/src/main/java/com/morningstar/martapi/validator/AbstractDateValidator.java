package com.morningstar.martapi.validator;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public abstract class AbstractDateValidator {

    private final DateTimeFormatter formatter;

    public AbstractDateValidator(String dateFormat) {
        this.formatter = DateTimeFormatter.ofPattern(dateFormat);

    }

    protected boolean isInvalidDateOrder(String inputStartDate, String inputEndDate) {
        try {
            LocalDate startDate = LocalDate.parse(inputStartDate, formatter);
            LocalDate endDate = LocalDate.parse(inputEndDate, formatter);
            return startDate.isAfter(endDate) || !inputStartDate.equals(startDate.format(formatter)) || !inputEndDate.equals(endDate.format(formatter));
        } catch (DateTimeParseException e) {
            return true;
        }
    }

    protected boolean isMissingDate(String inputStartDate, String inputEndDate) {
        return (StringUtils.isEmpty(inputStartDate) && StringUtils.isNotEmpty(inputEndDate)) ||
                (StringUtils.isNotEmpty(inputStartDate) && StringUtils.isEmpty(inputEndDate));
    }

    protected boolean isWithoutDateRange(String inputStartDate, String inputEndDate) {
        return StringUtils.isEmpty(inputStartDate) && StringUtils.isEmpty(inputEndDate);
    }
}
