package com.morningstar.martapi.util;

import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.util.JwtUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.UUID;

@Component
@NoArgsConstructor
public class InvestmentApiRequestUtil {

    public Mono<InvestmentResponse> getInvestmentResponse(MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway, InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime) {
        LoggerUtil loggerUtil = new LoggerUtil();
        var originalRequest = deepCopyInvestmentApiRequest(investmentApiRequest);
        return gridViewGateway.asyncRetrieveSecurities(investmentApiRequest)
                .doOnEach(LogHelper.logOnNext(list -> loggerUtil.logAccess(originalRequest, headersAndParams, startTime)))
                .doOnEach(LogHelper.logOnError(e -> loggerUtil.logError(originalRequest, headersAndParams, startTime, e)));
    }

    public HeadersAndParams getHeaderAndParams(String token, String productId, String requestId, String tsId, String serviceName) {
        return HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .tsId(tsId)
                .serviceName(StringUtils.isEmpty(serviceName) ? productId : serviceName)
                .build();
    }

    public InvestmentApiRequest getValidatedInvestmentApiRequest(InvestmentApiRequest investmentApiRequest, String token, String headerUserId, String productId, String requestId, String readCache, String checkEntitlement) {
        String userId = StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id"), headerUserId);
        String configId = JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
        investmentApiRequest.setUserId(userId);
        investmentApiRequest.setConfigId(configId);
        investmentApiRequest.setProductId(productId);
        investmentApiRequest.setRequestId(requestId);
        investmentApiRequest.setReadCache(readCache);
        investmentApiRequest.setCheckEntitlement("true".equals(checkEntitlement));
        return investmentApiRequest;
    }

    private InvestmentApiRequest deepCopyInvestmentApiRequest(InvestmentApiRequest original) {
        if (original == null) {
            return new InvestmentApiRequest();
        }
        
        InvestmentApiRequest copy = new InvestmentApiRequest();
        copy.setUserId(original.getUserId());
        copy.setConfigId(original.getConfigId());
        copy.setProductId(original.getProductId());
        copy.setRequestId(original.getRequestId());
        copy.setReadCache(original.getReadCache());
        copy.setCheckEntitlement(original.isCheckEntitlement());
        copy.setUseCase(original.getUseCase());

        if (original.getInvestments() != null) {
            copy.setInvestments(new ArrayList<>(original.getInvestments()));
        }
        if (original.getDataPoints() != null) {
            copy.setDataPoints(new ArrayList<>(original.getDataPoints()));
        }
        
        return copy;
    }
}
