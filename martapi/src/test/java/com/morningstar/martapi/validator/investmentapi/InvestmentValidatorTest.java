package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class InvestmentValidatorTest {

    private InvestmentValidator validator;

    @Before
    public void setup() {
        this.validator = new InvestmentValidator();
    }

    @Test
    public void validate() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());

        InvestmentApiRequest request2 = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request2));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateDuplicateInvestment() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01012").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request1));
        Assertions.assertEquals(Status.DUPLICATE_INVESTMENT.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateCorrectInput() {
        InvestmentApiRequest request1 = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("OP01012").build(),Investment.builder().id("OP01014").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        Assertions.assertDoesNotThrow(() -> validator.validate(request1));
    }
}
