package com.morningstar.martapi.validator.investmentapi;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.EquityDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Filter;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.FilterTypes;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.MongodbCollection;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Parameter;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;

public class ColumnLimitValidatorTest {

    private ColumnLimitValidator validator;
    private EquityDatapointUtil equityDatapointUtil;

    @Before
    public void setup() {
        this.equityDatapointUtil = new EquityDatapointUtil();
        this.validator = new ColumnLimitValidator(equityDatapointUtil);
    }

    @Test
    public void canValidate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP010").alias("HP010a").build(),
                        GridviewDataPoint.builder().dataPointId("HP022").alias("HP022a").build()))
                .build();
        validator.validate(request);
    }

    @Test
    public void canRejectLargeNumberOfColumns() {
        List<GridviewDataPoint> dataPoints = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            dataPoints.add(createTsDataPoint(i));
        }
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(dataPoints)
                .build();
        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        assertEquals(Status.BAD_REQUEST.getCode(), exception.getStatus().getCode());
        assertTrue(exception.getMessage().startsWith("Request input exceeds maximum column limit of [89421] columns"));
    }

    @Test
    public void canThrowErrorWhenReachMaxNumberOfColumns() {
        ColumnLimitValidator customValidator = new ColumnLimitValidator(1,equityDatapointUtil);
        List<GridviewDataPoint> dataPoints = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            dataPoints.add(createTsDataPoint(i));
        }
        dataPoints.add(createCurrentDataPoint());
        dataPoints.add(createEquitytDataPoint());
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(dataPoints)
                .build();
        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class,
                () -> customValidator.validate(request));
        assertEquals(Status.BAD_REQUEST.getCode(), exception.getStatus().getCode());
        assertTrue(exception.getMessage().startsWith("Request input exceeds maximum column limit of [1] columns"));
    }

    @Test
    public void doNotThrowErrorWhenReachMaxNumberOfColumnsForExcludedProductIds(){
        ColumnLimitValidator columnLimitValidator = new ColumnLimitValidator(10,equityDatapointUtil);
        List<GridviewDataPoint> dataPoints = List.of(createTimeSeriesEquityDatapoint());
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .productId("DWS")
                .dataPoints(dataPoints)
                .build();

        Pair<String,String> filterPair = Pair.of("period", StringUtils.EMPTY);

        Parameter dateParameter = Parameter.builder()
                .isMandatory(true)
                .isTimeField(true)
                .dbField("asOfDate")
                .filterType(FilterTypes.rangeFilter)
                .build();

        Filter filter = Filter.builder()
                .filterType(FilterTypes.rangeFilter)
                .attributes(filterPair)
                .dbField("document.reportPeriod")
                .isMandatory(true)
                .build();

        MongodbCollection collection = MongodbCollection.builder()
                .name("collection")
                .parameters(List.of(dateParameter))
                .filtersByLevel(Map.of(1, List.of(filter)))
                .idLevel("performanceId")
                .reqAttributes(List.of("period"))
                .build();

        DataPoint dp0 = DataPoint.builder().id("EQ000").groupName("collection").idLevel("performanceId").equityDatapoint(EquityDataPoint.builder().timeSeriesCollection(collection).timeSeriesField("document.column").tsLevel(1).build()).build();
        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQ000")).thenReturn(dp0);
            columnLimitValidator.validate(request);
        }

    }

    @Test
    public void testGroupDatapoint(){
        List<GridviewDataPoint> dataPoints = List.of(createTimeSeriesEquityDatapoint());
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .productId("mds")
                .dataPoints(dataPoints)
                .build();

        Parameter dateParameter = Parameter.builder()
                .isMandatory(true)
                .isTimeField(true)
                .dbField("asOfDate")
                .filterType(FilterTypes.rangeFilter)
                .build();

        MongodbCollection collection = MongodbCollection.builder()
                .name("collection")
                .parameters(List.of(dateParameter))
                .filtersByLevel(Map.of())
                .idLevel("performanceId")
                .reqAttributes(List.of())
                .frequency("a")
                .build();
        DataPoint sub0 = DataPoint.builder().id("EQ001").groupName("collection").idLevel("performanceId").equityDatapoint(EquityDataPoint.builder().timeSeriesCollection(collection).timeSeriesField("document.column1").tsLevel(1).build()).build();
        DataPoint sub1 = DataPoint.builder().id("EQ002").groupName("collection").idLevel("performanceId").equityDatapoint(EquityDataPoint.builder().timeSeriesCollection(collection).timeSeriesField("document.column2").tsLevel(1).build()).build();
        DataPoint dp0 = DataPoint.builder().id("EQ000").groupName("collection").idLevel("performanceId").subDataPoints(List.of(sub0,sub1)).equityDatapoint(EquityDataPoint.builder().timeSeriesCollection(collection).build()).build();
        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQ000")).thenReturn(dp0);
            validator.validate(request);
        }

    }

    private GridviewDataPoint createTsDataPoint(int i) {
        return GridviewDataPoint.builder()
                .dataPointId("HP026" + i)
                .alias("HP026a" + i)
                .startDate("1990-01-01")
                .endDate("2024-01-01")
                .frequency("d")
                .windowType("4")
                .windowSize("0")
                .stepSize("1")
                .build();
    }

    private GridviewDataPoint createCurrentDataPoint() {
        return GridviewDataPoint.builder()
                .dataPointId("123456")
                .alias("123456")
                .build();
    }

    private GridviewDataPoint createEquitytDataPoint() {
        return GridviewDataPoint.builder()
                .alias("equity")
                .build();
    }

    private GridviewDataPoint createTimeSeriesEquityDatapoint(){
        return GridviewDataPoint.builder()
                .dataPointId("EQ000")
                .alias("EQ000")
                .period(Set.of("Q1","Q2"))
                .startDate("2000-01-01")
                .endDate("2001-12-31")
                .frequency("m")
                .build();
    }
}