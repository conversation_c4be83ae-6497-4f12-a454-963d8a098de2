package com.morningstar.martapi.validator.investmentapi;

import static org.mockito.Mockito.mockStatic;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.MockedStatic;

public class DynamicDatapointValidatorTest {

    private DynamicDatapointValidator validator;

    @Before
    public void setup() {
        this.validator = new DynamicDatapointValidator();
    }

    @Test
    public void validateParameterEuTaxonomyObjective() {
        List<String> datapointIds=new ArrayList<>();
        datapointIds.add("EUVFZ");
        List<String> dynamicParams = List.of("euTaxonomyObjective", "Climate Change Adaptation (CCA)");
        DataPoint datapointDetails = DataPointRepository.getByNid("EUVFZ");
        datapointDetails.setDatapointToParams(Map.of("EUVFZ", dynamicParams));
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointIds(List.of("EUVFZ"));
        Map<String, List<String>> requiredParams = new HashMap<>();
        requiredParams.put("EUVFZ", List.of("euTaxonomyObjective"));

        DataPoint dp1 = DataPoint.builder().id("EUVFZ").groupName("CDeu_taxonomy_objective").idLevel("CompanyId").parameterName("euTaxonomyObjective").datapointToParams(requiredParams).build();

        GridviewDataPoint gridviewReq = GridviewDataPoint.builder()
                .dataPointIds(datapointIds)
                .euTaxonomyObjective(dynamicParams)
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(gridviewReq))
                .build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EUVFZ")).thenReturn(dp1);
            Assertions.assertDoesNotThrow(() -> validator.validate(request));
        }
    }

    @Test
    public void validateParameterEuTaxonomyActivityClusterType() {
        List<String> datapointIds=new ArrayList<>();
        datapointIds.add("EUVFT");
        List<String> dynamicParams = List.of("euTaxonomyActivityClusterType", "Climate Change Adaptation (CCA)");
        DataPoint datapointDetails = DataPointRepository.getByNid("EUVFT");
        datapointDetails.setDatapointToParams(Map.of("EUVFT", dynamicParams));
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointIds(List.of("EUVFT"));
        Map<String, List<String>> requiredParams = new HashMap<>();
        requiredParams.put("EUVFT", List.of("euTaxonomyActivityClusterType"));

        DataPoint dp1 = DataPoint.builder().id("EUVFT").groupName("CDeu_taxonomy_objective").idLevel("CompanyId").parameterName("euTaxonomyActivityClusterType").datapointToParams(requiredParams).build();

        GridviewDataPoint gridviewReq = GridviewDataPoint.builder()
                .dataPointIds(datapointIds)
                .euTaxonomyActivityClusterType(dynamicParams)
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(gridviewReq))
                .build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EUVFT")).thenReturn(dp1);
            Assertions.assertDoesNotThrow(() -> validator.validate(request));
        }
    }

    @Test
    public void validateParameterEuTaxonomyNonNGObjective() {
        List<String> datapointIds=new ArrayList<>();
        datapointIds.add("EUVFY");
        List<String> dynamicParams = List.of("euTaxonomyNonNGObjective", "Climate Change Adaptation (CCA)");
        DataPoint datapointDetails = DataPointRepository.getByNid("EUVFY");
        datapointDetails.setDatapointToParams(Map.of("EUVFY", dynamicParams));
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointIds(List.of("EUVFY"));
        Map<String, List<String>> requiredParams = new HashMap<>();
        requiredParams.put("EUVFY", List.of("euTaxonomyNonNGObjective"));

        DataPoint dp1 = DataPoint.builder().id("EUVFY").groupName("CDeu_taxonomy_objective").idLevel("CompanyId").parameterName("euTaxonomyNonNGObjective").datapointToParams(requiredParams).build();

        GridviewDataPoint gridviewReq = GridviewDataPoint.builder()
                .dataPointIds(datapointIds)
                .euTaxonomyNonNGObjective(dynamicParams)
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(gridviewReq))
                .build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EUVFY")).thenReturn(dp1);
            Assertions.assertDoesNotThrow(() -> validator.validate(request));
        }
    }

    @Test
    public void validateParameterError() {
        List<String> datapointIds=new ArrayList<>();
        datapointIds.add("EUVFY");
        List<String> dynamicParams = List.of("euTaxonomyNonNGObjective", "Climate Change Adaptation (CCA)");
        DataPoint datapointDetails = DataPointRepository.getByNid("EUVFY");
        datapointDetails.setDatapointToParams(Map.of("EUVFY", dynamicParams));
        GridviewDataPoint dp = new GridviewDataPoint();
        dp.setDataPointIds(List.of("EUVFY"));
        Map<String, List<String>> requiredParams = new HashMap<>();
        requiredParams.put("EUVFY", List.of("euTaxonomyNonNGObjective"));

        DataPoint dp1 = DataPoint.builder().id("EUVFY").groupName("CDeu_taxonomy_objective").idLevel("CompanyId").parameterName("euTaxonomyNonNGObjective").datapointToParams(requiredParams).build();

        GridviewDataPoint gridviewReq = GridviewDataPoint.builder()
                .dataPointIds(datapointIds)
                .euTaxonomyObjective(dynamicParams)
                .build();

        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(gridviewReq))
                .build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EUVFY")).thenReturn(dp1);
            InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                    () -> validator.validate(request));
            Assertions.assertEquals(Status.INVALID_DATAPOINT.getCode(),exception.getStatus().getCode());
        }
    }
}
