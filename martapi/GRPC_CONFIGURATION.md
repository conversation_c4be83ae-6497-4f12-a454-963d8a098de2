# gRPC 服务配置说明

## 概述

本项目支持通过环境变量控制gRPC服务的启用/禁用。默认情况下，gRPC服务是**禁用**的，只有在明确设置环境变量时才会启用。

## 配置方式

### 环境变量

- `ENABLE_GRPC`: 控制是否启用gRPC服务
  - `true`: 启用gRPC服务
  - `false` 或未设置: 禁用gRPC服务（默认）

### 应用配置

在 `application.yml` 中的配置：

```yaml
grpc:
  server:
    enabled: ${ENABLE_GRPC:false}  # 默认为false
    port: -1
    max-inbound-metadata-size: 32768
```

## 使用方法

### 启用gRPC服务

```bash
# Linux/Mac
export ENABLE_GRPC=true
java -jar martapi.jar

# 或者直接在启动时设置
ENABLE_GRPC=true java -jar martapi.jar

# Windows
set ENABLE_GRPC=true
java -jar martapi.jar
```

### 禁用gRPC服务（默认）

```bash
# 不设置环境变量，或者明确设置为false
export ENABLE_GRPC=false
java -jar martapi.jar

# 或者不设置环境变量（默认行为）
java -jar martapi.jar
```

## 影响的组件

当gRPC服务被禁用时，以下组件不会被加载：

1. `TimeSeriesGrpcService` - gRPC时间序列服务
2. `MetadataInterceptor` - gRPC元数据拦截器
3. gRPC服务器自动配置

## 验证方法

### 检查gRPC服务是否启用

1. 查看应用启动日志，如果gRPC启用，会看到类似以下日志：
   ```
   gRPC Server started, listening on address: 0.0.0.0, port: [端口号]
   ```

2. 如果gRPC禁用，不会看到gRPC相关的启动日志

### 测试gRPC服务

当gRPC启用时，可以使用gRPC客户端连接到服务进行测试。

## 注意事项

1. 修改环境变量后需要重启应用才能生效
2. 默认情况下gRPC服务是禁用的，这样可以减少不必要的资源消耗
3. 在生产环境中，建议根据实际需求决定是否启用gRPC服务
