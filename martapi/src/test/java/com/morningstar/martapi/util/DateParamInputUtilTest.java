package com.morningstar.martapi.util;

import org.junit.Assert;
import org.junit.Test;

public class DateParamInputUtilTest {

    @Test
    public void testFormatDateParameter_ValidDate() {
        String inputDate = "2024-1-31";
        String expectedOutput = "2024-01-31";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_ValidDate2() {
        String inputDate = "2024-01-31";
        String expectedOutput = "2024-01-31";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_ValidDateSingleDigitMonthAndDay() {
        String inputDate = "2023-9-5";
        String expectedOutput = "2023-09-05";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_EmptyString() {
        String inputDate = "";
        String expectedOutput = "";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_NullInput() {
        String inputDate = null;
        String expectedOutput = "";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_InvalidDate() {
        String inputDate = "2024-13-01"; // Invalid month
        String expectedOutput = "";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testFormatDateParameter_InvalidFormat() {
        String inputDate = "01/31/2024"; // Invalid format
        String expectedOutput = "";
        String actualOutput = DateParamInputUtil.formatDateParameter(inputDate);
        Assert.assertEquals(expectedOutput, actualOutput);
    }
}
