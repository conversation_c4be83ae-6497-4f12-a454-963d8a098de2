package com.morningstar.martapi.grpc;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.grpc.proto.TimeSeriesRequest;
import com.morningstar.martapi.grpc.service.TimeSeriesGrpcService;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.timeseries.entity.*;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.never;

class TimeSeriesGrpcServiceTest {

    private TimeSeriesGrpcService timeSeriesGrpcService;
    private MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    @BeforeEach
    void setUp() {
        tsOldRspGateway = mock(MartGateway.class);
        validator = mock(RequestValidationHandler.class);
        timeSeriesGrpcService = new TimeSeriesGrpcService(tsOldRspGateway, validator);
    }


    @Test
    void testRetrieveTimeSeriesDataSuccess() {

        TimeSeriesRequest grpcRequest = TimeSeriesRequest.newBuilder()
                .addInvestmentIds("E0GBR017IT")
                .addDataPoints("HPD10")
                .setCurrency("USD")
                .setStartDate("20230101")
                .setEndDate("20231231")
                .build();

        TSResponse mockResponse = createTsResponse();
        when(tsOldRspGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(mockResponse));
        StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> observer = mock(StreamObserver.class);


        timeSeriesGrpcService.retrieveTimeSeriesData(grpcRequest, observer);


        verify(observer, timeout(1000)).onNext(any());
        verify(observer, timeout(1000)).onCompleted();
        verify(observer, never()).onError(any());
    }
    private static TSResponse createTsResponse() {
        TSResponse mockResponse = new TSResponse();
        mockResponse.setStatus(new TSStatus("200", "OK"));

        TSItem tsItem = new TSItem();
        tsItem.setSecid("E0GBR017IT");
        tsItem.setDataid("HPD10");

        TSData tsData = new TSData();
        tsData.setDate(45349);
        tsData.setValue(123.45);

        List<TSData> dataList = new ArrayList<>();
        dataList.add(tsData);
        tsItem.setData(dataList);

        List<TSItem> items = new ArrayList<>();
        items.add(tsItem);

        TSContent tsContent = new TSContent();
        tsContent.setItems(items);
        mockResponse.setContent(tsContent);
        return mockResponse;
    }
}
