#!/bin/bash

echo "=== gRPC 配置测试脚本 ==="
echo ""

# 测试1: 检查默认情况下gRPC是否禁用
echo "测试1: 检查默认配置（gRPC应该被禁用）"
echo "编译项目..."
mvn clean compile -DskipTests -q

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    echo "检查gRPC相关类是否有条件注解..."
    
    # 检查TimeSeriesGrpcService是否有ConditionalOnProperty注解
    if grep -q "@ConditionalOnProperty" src/main/java/com/morningstar/martapi/grpc/service/TimeSeriesGrpcService.java; then
        echo "✓ TimeSeriesGrpcService 有条件注解"
    else
        echo "✗ TimeSeriesGrpcService 缺少条件注解"
    fi
    
    # 检查MetadataInterceptor是否有ConditionalOnProperty注解
    if grep -q "@ConditionalOnProperty" src/main/java/com/morningstar/martapi/grpc/interceptor/MetadataInterceptor.java; then
        echo "✓ MetadataInterceptor 有条件注解"
    else
        echo "✗ MetadataInterceptor 缺少条件注解"
    fi
    
    # 检查application.yml中的配置
    if grep -q "enabled: \${ENABLE_GRPC:false}" src/main/resources/application.yml; then
        echo "✓ application.yml 配置正确（默认禁用gRPC）"
    else
        echo "✗ application.yml 配置不正确"
    fi
    
else
    echo "✗ 编译失败"
fi

echo ""
echo "=== 配置验证完成 ==="
echo ""
echo "使用说明："
echo "1. 默认情况下gRPC服务是禁用的"
echo "2. 要启用gRPC服务，设置环境变量: export ENABLE_GRPC=true"
echo "3. 要禁用gRPC服务，设置环境变量: export ENABLE_GRPC=false 或不设置该变量"
echo ""
echo "启动示例："
echo "# 启用gRPC"
echo "ENABLE_GRPC=true java -jar target/martapi.jar"
echo ""
echo "# 禁用gRPC（默认）"
echo "java -jar target/martapi.jar"
