package com.morningstar.martapi.config;

import com.morningstar.dataac.martgateway.core.common.config.CallerIdRegistry;
import com.morningstar.dataac.martgateway.core.common.entity.ClearCacheRequest;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.AuthTokenValidator;
import com.morningstar.martapi.validator.ProductIdValidator;
import com.morningstar.martapi.validator.RequestIdValidator;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.ServiceNameValidator;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martapi.validator.delta.DeltaStartTimeValidator;
import com.morningstar.martapi.validator.economicdata.EconomicDataDateValidator;
import com.morningstar.martapi.validator.economicdata.EconomicDataValidator;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martapi.validator.investmentapi.ColumnLimitValidator;
import com.morningstar.martapi.validator.investmentapi.DataPointValidator;
import com.morningstar.martapi.validator.investmentapi.DateValidator;
import com.morningstar.martapi.validator.investmentapi.IdTypeValidator;
import com.morningstar.martapi.validator.investmentapi.InvestmentValidator;
import com.morningstar.martapi.validator.investmentapi.DynamicDatapointValidator;
import com.morningstar.martapi.validator.investmentapi.UseCaseValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDataPointValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDateValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestInvestmentValidator;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

@Configuration
public class ValidatorsConfig {

    @Value("${authentication.jwt.issuer}")
    private String tokenIssuer;

    @Bean(name = "investmentApiValidator")
    public RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> investmentApiValidator(
            @Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry,
            @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(tokenIssuer),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator(),
                new ServiceNameValidator(serviceNameRegistry)
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(1000, new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator(),
                new DynamicDatapointValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "deltaDetectionApiValidator")
    public RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator(
            @Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry,
            @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(tokenIssuer),
                new ProductIdValidator(productIdsRegistry),
                new ServiceNameValidator(serviceNameRegistry),
                new RequestIdValidator()
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new DeltaStartTimeValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "asyncApiValidator")
    public RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> asyncDataApiValidator(
            @Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry,
            @Qualifier("dataEntitlementService") DataEntitlementService<InvestmentApiRequest> dataEntitlementService
            ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(tokenIssuer),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators, dataEntitlementService);
    }

    @Bean(name = "timeSeriesApiValidator")
    public RequestValidationHandler<HeadersAndParams, MartRequest> timeSeriesApiValidator(
            @Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry,
            @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(tokenIssuer),
                new ProductIdValidator(productIdsRegistry),
                new ServiceNameValidator(serviceNameRegistry),
                new RequestIdValidator()
        );
        List<Validator<MartRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(ValidationException::new),
                new TsMartRequestDataPointValidator(),
                new TsMartRequestInvestmentValidator(),
                new TsMartRequestDateValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "clearCacheValidator")
    public RequestValidationHandler<HeadersAndParams, ClearCacheRequest> clearCacheValidator(@Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(tokenIssuer), new ServiceNameValidator(serviceNameRegistry));
        List<Validator<ClearCacheRequest>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.clearcache.InvestmentValidator(),
                new com.morningstar.martapi.validator.clearcache.DataPointValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "licenseAuditValidator")
    public RequestValidationHandler<HeadersAndParams, LicenseAuditEntity> licenseAuditValidator(@Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry, @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(tokenIssuer), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry), new ServiceNameValidator(serviceNameRegistry));
        List<Validator<LicenseAuditEntity>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.licenseapi.LicenseAuditEntityValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "licenseCellValidator")
    public RequestValidationHandler<HeadersAndParams, LicenseCellEntity> licenseCellValidator(@Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry, @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(tokenIssuer), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry), new ServiceNameValidator(serviceNameRegistry));
        List<Validator<LicenseCellEntity>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.licenseapi.LicenseCellEntityValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "economicDataValidator")
    public RequestValidationHandler<HeadersAndParams, EconomicDataRequest> economicDataValidator(@Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry, @Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(tokenIssuer), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry), new ServiceNameValidator(serviceNameRegistry));
        List<Validator<EconomicDataRequest>> requestBodyValidators = List.of(
                new EconomicDataDateValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "economicManadatoryDataValidator")
    public RequestValidationHandler<HeadersAndParams, EconomicDataRequest> economicManadatoryDataValidator(@Qualifier("productIdsRegistry") CallerIdRegistry productIdsRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(tokenIssuer), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry));
        List<Validator<EconomicDataRequest>> requestBodyValidators = List.of(
        new EconomicDataValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "securityValidator")
    public RequestValidationHandler<HeadersAndParams, MartRequest> securityValidator(@Qualifier("serviceNameRegistry") CallerIdRegistry serviceNameRegistry) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new RequestIdValidator(), new ServiceNameValidator(serviceNameRegistry));
        List<Validator<MartRequest>> requestBodyValidators = Collections.emptyList();
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }
}
