package com.morningstar.martapi.validator.delta;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;

import java.time.Duration;
import java.time.Instant;

public class DeltaStartTimeValidator implements Validator<InvestmentApiRequest> {

    protected static final int DELTA_START_TIME_DAY_LIMIT = 45;

    @Override
    public void validate(InvestmentApiRequest request) throws RuntimeException {
        Instant deltaStartTime = request.getDeltaStartTime();
        if (request.isDeltaDetection()) {
            validateMandatoryDeltaStartTime(deltaStartTime);
        }

        if (deltaStartTime != null && isInvalidDeltaStartTime(deltaStartTime)) {
            throw new InvestmentApiValidationException(Status.INVALID_DELTA_START_TIME);
        }
    }

    private void validateMandatoryDeltaStartTime(Instant deltaStartTime) {
        if (deltaStartTime == null) {
            throw new InvestmentApiValidationException(Status.INVALID_DELTA_START_TIME);
        }
    }

    private boolean isInvalidDeltaStartTime(Instant deltaStartTime) {
        Instant now = Instant.now();
        return deltaStartTime.isAfter(now) || deltaStartTime.isBefore(now.minus(Duration.ofDays(DELTA_START_TIME_DAY_LIMIT)));
    }
}
