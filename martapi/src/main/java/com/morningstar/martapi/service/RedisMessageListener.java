package com.morningstar.martapi.service;

import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderScanner;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import io.lettuce.core.pubsub.RedisPubSubListener;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisMessageListener implements RedisPubSubListener<String, String> {

    private static final Set<String> runningChannels = ConcurrentHashMap.newKeySet();

    private final DataPointLoaderScanner dataPointLoaderScanner;
    private final LocalIdMapperCache localIdMapperCache;
    private final EntitlementCacheService entitlementCacheService;
    private final UimTokenService uimTokenService;

    public RedisMessageListener(DataPointLoaderScanner dataPointLoaderScanner, LocalIdMapperCache localIdMapperCache,
                                EntitlementCacheService entitlementCacheService, UimTokenService uimTokenService) {
        this.dataPointLoaderScanner = dataPointLoaderScanner;
        this.localIdMapperCache = localIdMapperCache;
        this.entitlementCacheService = entitlementCacheService;
        this.uimTokenService = uimTokenService;
    }

    @Override
    public void message(String channel, String message) {
        log.info("Receive channel=\"{}\", message=\"{}\"", channel, message);
        if (channel == null || !runningChannels.add(channel)) {
            log.info("A request for channel [{}] is already running", channel);
            return;
        }
        try {
            if (channel.startsWith("config_update")) {
                updateDataPointMap();
            } else if (channel.startsWith("local_id_cache_update")) {
                localIdMapperCache.loadDeltaData();
            } else if (channel.startsWith("entitlement_cache_update")){
                entitlementCacheService.refreshDataPackageInfo();
                entitlementCacheService.refreshPckgMiscInfo();
                entitlementCacheService.refreshIndexMisc();
            } else if (channel.startsWith("uim_token_clear_cache")) {
                uimTokenService.clearLocalCache();
            } else {
                log.warn("undefined topic:{}", channel);
            }
        } finally {
            runningChannels.remove(channel);
        }
    }

    private void updateDataPointMap() {
        int beforeSize = DataPointRepository.getDataPointMapSize();
        //dataPointLoader.initialize("initialize");
        dataPointLoaderScanner.loadDataPoints();
        int afterSize = DataPointRepository.getDataPointMapSize();
        log.info(String.format(
                "update data point map complete, original size:%s, current size:%s",
                beforeSize,
                afterSize));
    }

    @Override
    public void message(String pattern, String channel, String message) {
        log.info("Receive pattern={}, channel={}, message={}", pattern, channel, message);
    }

    @Override
    public void psubscribed(String pattern, long count) {
        log.info("Subscribed pattern={}, current_count={}", pattern, count);
    }

    @Override
    public void punsubscribed(String pattern, long count) {
        log.info("Unsubscribed pattern={}, current_count={}", pattern, count);
    }

    @Override
    public void subscribed(String channel, long count) {
        log.info("Subscribed channel={}, current_count={}", channel, count);
    }

    @Override
    public void unsubscribed(String channel, long count) {
        log.info("Unsubscribed channel={}, current_count={}", channel, count);
    }

}
