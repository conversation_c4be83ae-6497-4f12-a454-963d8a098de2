package com.morningstar.martapi.validator;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

public class RequestIdValidator implements Validator<HeadersAndParams> {

    @Override
    public void validate(HeadersAndParams headersAndParams) throws RuntimeException {
        if (StringUtils.isEmpty(headersAndParams.getRequestId()) || !isUUID(headersAndParams.getRequestId())) {
            throw new InvestmentApiValidationException(Status.INVALID_REQUEST_ID);
        }
    }

    private boolean isUUID(String text) {
        try {
            UUID.fromString(text);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
