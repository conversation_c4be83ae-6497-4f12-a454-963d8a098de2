package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class TsMartRequestInvestmentValidator implements Validator<MartRequest> {

	@Override
	public void validate(MartRequest request) throws ValidationException {
		List<String> investments = request.getIds();
		if (CollectionUtils.isEmpty(investments)) {
			Status status = Status.MISSING_ATTRIBUTE
					.withMessage("Request input missing mandatory attribute - investments");
			throw new ValidationException(status);
		}
		Set<String> distinctInvestments = new HashSet<>();
		for (String id : investments) {
			if (StringUtils.isEmpty(id)) {
				Status status = Status.MISSING_ATTRIBUTE
						.withMessage("Request input missing mandatory attribute - investment id");
				throw new ValidationException(status);
			}
			if (!distinctInvestments.add(id)) {
				Status status = Status.DUPLICATE_INVESTMENT;
				throw new ValidationException(status);
			}
		}
	}
}
