package com.morningstar.martapi.controller;

import com.morningstar.martgateway.cloud.ecs.metadata.AwsEcsServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = HealthCheckController.class)
@TestPropertySource(properties = {"spring.profiles=stg","martgateway.application=martapi"})
public class HealthCheckControllerTest {
    @Autowired
    private HealthCheckController healthCheckController;
    @MockBean
    private AwsEcsServiceClient awsEcsServiceClient;

    @Value("${spring.profiles}")
    private String env;

    @Value("${martgateway.application}")
    private String application;

    @Before
    public void setup() {
        Mockito.when(awsEcsServiceClient.getBlueGreenOrDefault(Mockito.anyString())).thenReturn("green");
    }

    @Test
    public void testCheckHealth() {
        {
            ResponseEntity<String> response = healthCheckController.checkHealth(true);
            Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
            Assert.assertEquals(
                    String.format("System is healthy. Environment: %s, B/G env: green, Application: %s", env, application),
                    response.getBody()
            );
        }
        {
            ResponseEntity<String> response = healthCheckController.checkHealth(false);
            Assert.assertEquals(
                    String.format("System is healthy. Environment: %s, B/G env: (option not turned on), Application: %s", env, application),
                    response.getBody()
            );
        }
    }

    @Test
    public void testTSCheckHealth() {
        {
            ResponseEntity<String> response = healthCheckController.checkTimeSeriesClusterHealth(true);
            Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
            Assert.assertEquals(
                    String.format("System is healthy. Environment: %s, B/G env: green, Application: %s", env, application),
                    response.getBody()
            );
        }
        {
            ResponseEntity<String> response = healthCheckController.checkTimeSeriesClusterHealth(false);
            Assert.assertEquals(
                    String.format("System is healthy. Environment: %s, B/G env: (option not turned on), Application: %s", env, application),
                    response.getBody()
            );
        }
    }
}
