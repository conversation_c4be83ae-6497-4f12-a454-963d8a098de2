package com.morningstar.martapi.validator.economicdata;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.AbstractDateValidator;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;

public class EconomicDataDateValidator extends AbstractDateValidator implements Validator<EconomicDataRequest> {

    public EconomicDataDateValidator() {
        super("yyyy-MM-dd");
    }

    @Override
    public void validate(EconomicDataRequest request) throws RuntimeException {
        if (isWithoutDateRange(request.getStartDate(), request.getEndDate()) ||
                isMissingDate(request.getStartDate(), request.getEndDate())  ||
                isInvalidDateOrder(request.getStartDate(), request.getEndDate())
        ) {
            throw new InvestmentApiValidationException(Status.INVALID_DATE_SETTING);
        }
    }
}
