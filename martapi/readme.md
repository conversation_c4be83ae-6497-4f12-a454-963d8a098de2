## Preparation

> 1. Assume DATAAC aws account opeartor/readonly role.

> 2. If this kind of error happen in initialize process, change version of mart-gateway to 1.0.3-SNAPSHOT in pom.xml.
     "java.lang.UnsatisfiedLinkError: C:\Windows\System32\Calculation_CPP.dll: A dynamic link library (DLL) initialization routine failed"

## debug

> * data api: localhost:8080/v1/security-data/investment-id/{SecId}?dps={dps}&format=json&read-cache=false
> * data status api: localhost:8080/v1/security-status/{SecId}
> * get datapoint-meta: localhost:8080/v1/data-point/meta
> * ipdate datapoint-meta in local machine: localhost:8080/v1/data-point/update
> * refresh datapoint-meta in all instance: localhost:8080/v1/data-point/publish
> 
> ## /v1/holding-data debug
> 
> * You need to assume a role with access to the required DATASVC s3 buckets
> * aws sts assume-role --role-arn "{role-arn}"
> * Convenience script to do so: 
> * ```bash: printf "aws_access_key_id=%s\naws_secret_access_key=%s\naws_session_token=%s\n" $(aws sts assume-role --role-arn arn:aws:iam::270863951168:role/mart-role-stg --role-session-name MySessionName --query "Credentials.[AccessKeyId,SecretAccessKey,SessionToken]" --output text)```


## debug gprc service
> cd martapi
> mvn compile
> set environment variable grpc.serverr.port=9090