package com.morningstar.martapi.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.martapi.entity.TranscriptApiRequest;
import com.morningstar.martapi.entity.TranscriptApiResponse;
import com.morningstar.martapi.exception.TranscriptApiException;
import com.morningstar.martapi.util.InvestmentResponseTest;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class DocumentServiceTest {

    private MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway;
    private UimTokenService uimTokenService;
    private WebClient webClient;
    private WebClient.Builder builder;
    private DocumentService documentService;
    private DataEntitlementService dataEntitlementService;
    private EntitlementRequestFilterService entitlementInvestmentApiRequestFilterService;
    private RedisReactiveRepo documentTranscriptUrlCache;
    private HeadersAndParams headersAndParams;
    private long startTime;
    private String eventDate;
    private InvestmentApiRequest investmentApiRequest, investmentApiRequestTest;

    private static FilteredRequestData<InvestmentApiRequest> getFilteredRequestData(InvestmentApiRequest investmentApiRequest) {
        return new FilteredRequestData<>(investmentApiRequest, new ArrayList<>());
    }

    @Before
    public void setup() {
        gridViewGateway = mock(MartGateway.class);
        uimTokenService = mock(UimTokenService.class);
        webClient = Mockito.mock(WebClient.class);
        builder = mock(WebClient.Builder.class);
        dataEntitlementService = mock(DataEntitlementService.class);
        entitlementInvestmentApiRequestFilterService = mock(EntitlementRequestFilterService.class);
        documentTranscriptUrlCache = mock(RedisReactiveRepo.class);
        documentService = new DocumentService(gridViewGateway, builder, uimTokenService,dataEntitlementService,entitlementInvestmentApiRequestFilterService, documentTranscriptUrlCache);
        headersAndParams = HeadersAndParams.builder().
                authorizationToken(createToken()).
                requestId("reqId").
                productId("productId").build();
        startTime = System.currentTimeMillis();
        eventDate = "2024-01-01T00:00:00Z";
        ReflectionTestUtils.setField(documentService, "baseUrl", "https://mock-url.com");
        ReflectionTestUtils.setField(documentService, "webClient", webClient);
        investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .readCache("Yes")
                .investments(Collections.singletonList(Investment.builder().id("test_performance_id").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("EQOO4").build()))
                .build();
        FilteredRequestData<InvestmentApiRequest> filteredRequestData = getFilteredRequestData(investmentApiRequest);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(filteredRequestData);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(investmentApiRequest));
    }

    @Test()
    public void getDocumentFromRawJsonUrl() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        String rawUrl = "https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/";
        transcriptApiResponse.setRawJsonUrl(rawUrl);

        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("test_performance_id").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("EQOO4").build()))
                .build();
        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.empty());
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponse()));
        when(uimTokenService.getUimToken("Document Service")).thenReturn(createToken());

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);
        verify(documentTranscriptUrlCache, times(1)).getString(any());
        StepVerifier.create(result)
                .assertNext(inputStream -> {
                    byte[] bytes = inputStream.readAllBytes();
                    String resultData = new String(bytes, StandardCharsets.UTF_8);
                    assertEquals("Sample JSON response", resultData);
                })
                .verifyComplete();
    }

    @Test()
    public void getDocumentFromCache() {

        String rawUrl = "https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/";
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("test_performance_id").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("EQOO4").build()))
                .build();
        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.just(rawUrl));
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponse()));
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .assertNext(inputStream -> {
                    byte[] bytes = inputStream.readAllBytes();
                    String resultData = new String(bytes, StandardCharsets.UTF_8);
                    assertEquals("Sample JSON response", resultData);
                    verify(documentTranscriptUrlCache, times(1)).getString(any());
                    verify(documentTranscriptUrlCache, times(0)).setexString(any(),any(),any());
                })
                .verifyComplete();
    }

    @Test()
    public void getDocumentFromValidatedJsonUrl() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        when(dataEntitlementService.getEntitlement(any(),any())).thenReturn(getBasicCachedEntitlement(true));
        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.empty());
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponse()));
        when(uimTokenService.getUimToken("Document Service")).thenReturn(createToken());

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);
        StepVerifier.create(result)
                .assertNext(inputStream -> {
                    byte[] bytes = inputStream.readAllBytes();
                    String resultData = new String(bytes, StandardCharsets.UTF_8);
                    assertEquals("Sample JSON response", resultData);
                    verify(documentTranscriptUrlCache, times(1)).getString(any());
                    verify(documentTranscriptUrlCache, times(1)).setexString(any(), any(), any());
                })
                .verifyComplete();
    }

    @Test()
    public void getFailureFromDocumentEndPoint() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.empty());
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponseFailure()));
        when(uimTokenService.getUimToken("Document Service")).thenReturn(createToken());

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof TranscriptApiException
                        && throwable.getMessage().equals("Error making POST request to Transcript API"))
                .verify();
    }

    @Test()
    public void getFailureFromDocumentEndPointNoDataPoints() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        investmentApiRequestTest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("test_performance_id").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("EQOO4").build()))
                .build();

        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.empty());
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponseFailure()));
        when(uimTokenService.getUimToken("Document Service")).thenReturn(createToken());

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequestTest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof TranscriptApiException
                        && throwable.getMessage().equals("Error making POST request to Transcript API"))
                .verify();
    }

    @Test()
    public void getFailureFromDocumentEndPointEmptyDataPoints() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        investmentApiRequestTest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("test_performance_id").build()))
                .dataPoints(Collections.emptyList())
                .build();

        FilteredRequestData<InvestmentApiRequest> filteredRequestData = getFilteredRequestData(investmentApiRequestTest);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(filteredRequestData);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(investmentApiRequestTest));

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequestTest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof TranscriptApiException
                        && throwable.getMessage().equals("Investment API request failed - Missing required data"))
                .verify();
    }

    @Test()
    public void getFailureFromDocumentEndPointEmptyInvestments() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        investmentApiRequestTest = InvestmentApiRequest.builder()
                .useCase("view")
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("EQOO4").build()))
                .investments(Collections.emptyList())
                .build();

        FilteredRequestData<InvestmentApiRequest> filteredRequestData = getFilteredRequestData(investmentApiRequestTest);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(filteredRequestData);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(investmentApiRequestTest));

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequestTest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof TranscriptApiException
                        && throwable.getMessage().equals("Investment API request failed - Missing required data"))
                .verify();
    }

    @Test()
    public void getFailureFromDocumentEndPointCurrentPairData() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        when(documentTranscriptUrlCache.getString(any())).thenReturn(Mono.empty());
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(InvestmentResponseTest.createCustomInvestmentResponseFailureCurrentPair()));
        when(uimTokenService.getUimToken("Document Service")).thenReturn(createToken());

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof TranscriptApiException
                        && throwable.getMessage().equals("Error making POST request to Transcript API"))
                .verify();
    }

    @Test(expected = EntitlementException.class)
    public void getFailureFromDocumentEndPointEntitlementMissing() {

        TranscriptApiResponse transcriptApiResponse = new TranscriptApiResponse();
        transcriptApiResponse.setJobId("jobId");
        transcriptApiResponse.setValidatedJsonUrl("https://mstar-s3-dc-prd-speech-processed-audio-us-east-1.s3.amazonaws.com/");

        when(dataEntitlementService.getEntitlement(any(),any())).thenReturn(getBasicCachedEntitlement(false));

        mocksForWebClientPost(transcriptApiResponse);
        mocksForWebClientGet();

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof EntitlementException
                        && throwable.getMessage().equals("User is not entitled to access the package"))
                .verify();
    }

    @Test(expected = RuntimeException.class)
    public void getDocument_shouldLogErrorAndReturnEmptyStream_whenExceptionOccurs() {

        when(gridViewGateway.asyncRetrieveSecurities(any()))
                .thenThrow(new RuntimeException("Simulated error"));

        Mono<ByteArrayInputStream> result = documentService.getDocument(investmentApiRequest, headersAndParams, startTime, eventDate);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException
                        && throwable.getMessage().equals("Simulated error"))
                .verify();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }

    private void mocksForWebClientPost(TranscriptApiResponse transcriptApiResponse) {
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(webClient.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.header(eq("Authorization"), anyString())).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(any(TranscriptApiRequest.class))).thenReturn(requestHeadersSpec);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

        when(responseSpec.bodyToMono(TranscriptApiResponse.class))
                .thenReturn(Mono.just(transcriptApiResponse));
    }

    private void mocksForWebClientGet() {

        WebClient.RequestHeadersUriSpec requestHeadersUriSpec = mock(WebClient.RequestHeadersUriSpec.class);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestHeadersUriSpec.uri(any(URI.class))).thenReturn(requestHeadersSpec1);
        when(requestHeadersSpec1.header(eq("Accept"), anyString())).thenReturn(requestHeadersSpec1);
        when(requestHeadersSpec1.header(eq("User-Agent"), anyString())).thenReturn(requestHeadersSpec1);

        WebClient.ResponseSpec responseSpec1 = mock(WebClient.ResponseSpec.class);
        when(requestHeadersSpec1.retrieve()).thenReturn(responseSpec1);

        when(responseSpec1.onStatus(any(), any())).thenReturn(responseSpec1);
        when(responseSpec1.bodyToMono(String.class)).thenReturn(Mono.just("Sample JSON response"));

    }

    private CachedEntitlement getBasicCachedEntitlement(boolean isEntitled) {
        CachedEntitlement entitlement = new CachedEntitlement();
        CellMatch c = new CellMatch();
        // Set to Transcript Entitlement Package 941
        if(isEntitled) {
            c.setPackageId("941");
        } else {
            c.setPackageId("81");
        }
        entitlement.setCellMatchList(List.of(c));
        entitlement.setPortfolioSuppressionClientId("portfolioSuppressionClientId");
        return entitlement;
    }

}