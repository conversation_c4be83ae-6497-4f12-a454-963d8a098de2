package com.morningstar.martapi.validator.investmentapi;

import static com.morningstar.martapi.config.Constant.VALID_USE_CASES;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.UseCaseRequest;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

public class UseCaseValidator<T extends UseCaseRequest, E extends RuntimeException> implements Validator<T> {

    private final Function<Status, E> exceptionSupplier;

    public UseCaseValidator(Function<Status, E> exceptionSupplier) {
        this.exceptionSupplier = exceptionSupplier;
    }

    @Override
    public void validate(UseCaseRequest request) throws ValidationException {
        if (StringUtils.isEmpty(request.getUseCase()) || !VALID_USE_CASES.contains(request.getUseCase().toLowerCase())) {
            throw exceptionSupplier.apply((Status.INVALID_USE_CASE));
        }
    }
}
