package com.morningstar.martapi.controller;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@RestController
@RequestMapping(value = {"/v1/security-data","/investment-api/v1/security-data"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@Slf4j
public class DataPointController {
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.setSerializationInclusion(Include.NON_NULL);
    }
    @GetMapping(value = "/datapoints/{idlist}")
    public Mono<String> getDatapointValues(
            @PathVariable("idlist") String idList,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName
    ) {
        List<String> rawList = Arrays.asList(idList.split(","));
        List<DataPoint> dpList = new ArrayList<>();
        rawList.forEach(id -> dpList.add(DataPointRepository.getByNid(id)));
        String json = "";

        try {
            json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dpList);
            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId));
        } catch (IOException e) {
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, String.format("Datapoint controller json parse exception for id: %s", idList)),
                    new LogEntity(EXCEPTION_MESSAGE, e),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId));
        }

        return Mono.just(json);
    }

}
