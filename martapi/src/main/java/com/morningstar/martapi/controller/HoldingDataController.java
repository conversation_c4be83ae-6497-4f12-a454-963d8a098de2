package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.holdingresponse.HoldingResponse;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import io.swagger.annotations.ApiParam;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = "/v1/holding-data",
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class HoldingDataController {
    private final MartGateway<HoldingResponse, MartRequest> holdingDataGateway;

    private final RequestValidationHandler<HeadersAndParams, MartRequest> holdingApiValidator;

    @Inject
    public HoldingDataController(MartGateway<HoldingResponse, MartRequest> holdingDataGateway, @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams, MartRequest> holdingApiValidator) {
        this.holdingDataGateway = holdingDataGateway;
        this.holdingApiValidator = holdingApiValidator;
    }

    @GetMapping(value = "/investment-id/{id}")
    public Mono<?> getData(
            @ApiParam(example = "0P00000AWG")
            @PathVariable("id") String id,
            @ApiParam(example = "PMP03")
            @RequestParam(value = "dps", required = true) String dpList,
            @RequestParam(value = "top", required = false, defaultValue = "10") Integer top,
            @RequestParam(value = "readCache", required = false, defaultValue = "false") String readCache,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String userId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName,
            ServerHttpRequest request) {
        long startTime = System.currentTimeMillis();

        IdDatePair idDate = new IdDatePair();
        idDate.setId(id);
        idDate.setDates(Arrays.asList(LocalDate.now()));

        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .productId(productId)
                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())
                .serviceName(serviceName)
                .tsId(tsId)
                .build();

        holdingApiValidator.validateHeadersAndParams(headersAndParams);

        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList(dpList.split(",")))
                .idPairs(List.of(idDate))
                .readCache(readCache)
                .productId(productId)
                .requestId(requestId)
                .userId(userId)
                .top(top)
                .build();

        return holdingDataGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -> {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
        })).doOnEach(LogHelper.logOnError(e -> {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
            );
        }));
    }
}
