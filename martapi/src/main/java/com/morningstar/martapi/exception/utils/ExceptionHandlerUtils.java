package com.morningstar.martapi.exception.utils;

import static com.morningstar.martapi.config.Constant.X_API_PRODUCT_ID;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ServerWebExchange;

public class ExceptionHandlerUtils {
	public static String extractProductId(ServerWebExchange ex) {
		String productId = "";
		List<String> ids = ex.getRequest().getHeaders().get(X_API_PRODUCT_ID);
		if(ids != null && !ids.isEmpty()) {
			productId = ids.get(0);
		}
		return productId;
	}
	public static HttpStatus extractStatusCode(Status status) {
		String code = status.getCode();
		code = code.substring(0, Math.min(3, code.length()));
		return HttpStatus.valueOf(Integer.parseInt(code));
	}
}
