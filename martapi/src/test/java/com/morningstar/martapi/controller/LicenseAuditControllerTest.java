package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.service.LicenseAuditService;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.licenseapi.LicenseAuditEntityValidator;
import com.morningstar.martapi.validator.licenseapi.LicenseCellEntityValidator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LicenseAuditControllerTest {
    LicenseAuditController licenseAuditController;
    LicenseAuditService licenseAuditService;

    @Before
    public void setup() {
        licenseAuditService = mock(LicenseAuditService.class);
        licenseAuditController = new LicenseAuditController(licenseAuditService, new RequestValidationHandler<>(Collections.emptyList(), List.of(new LicenseAuditEntityValidator())), new RequestValidationHandler<>(Collections.emptyList(), List.of(new LicenseCellEntityValidator())));
    }

    @Test
    public void testGetAuditSuccess() {
        when(licenseAuditService.processAudit(any(), any(), any(), any(), anyBoolean())).thenReturn(new LicenseAuditResponse());
        LicenseAuditEntity request = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), new HashSet<>(Arrays.asList("inv1")));
        ResponseEntity<?> response = licenseAuditController.getAudit(request, "product1", "token1", "req1", "", "");
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testGetAuditFail() {
        LicenseAuditEntity request1 = new LicenseAuditEntity(Collections.emptySet(), new HashSet<>(Arrays.asList("inv1")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAudit(request1, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request2 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1", "  ")), new HashSet<>(Arrays.asList("inv1")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAudit(request2, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request3 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), Collections.emptySet());
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAudit(request3, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request4 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), new HashSet<>(Arrays.asList("inv1", "  ")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAudit(request4, "product1","token1", "req1", "", ""));
    }

    @Test
    public void testGetSummarySuccess() {
        when(licenseAuditService.processAudit(any(), any(), any(), any(), anyBoolean())).thenReturn(new LicenseAuditResponse());
        LicenseAuditEntity request = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), new HashSet<>(Arrays.asList("inv1")));
        ResponseEntity<?> response = licenseAuditController.getAuditSummary(request, "product1","token1", "req1", "", "");
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testGetSummaryFail() {
        LicenseAuditEntity request1 = new LicenseAuditEntity(Collections.emptySet(), new HashSet<>(Arrays.asList("inv1")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAuditSummary(request1, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request2 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1", "  ")), new HashSet<>(Arrays.asList("inv1")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAuditSummary(request2, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request3 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), Collections.emptySet());
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAuditSummary(request3, "product1","token1", "req1", "", ""));

        LicenseAuditEntity request4 = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), new HashSet<>(Arrays.asList("inv1", "  ")));
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getAuditSummary(request4, "product1","token1", "req1", "", ""));
    }

    @Test
    public void testGetCellInspection() {
        when(licenseAuditService.processCellInspection(any(),any(),any(),any())).thenReturn(new LicenseCellResponse());
        LicenseCellEntity cellRequest = LicenseCellEntity.builder().dataPointId("dp1").investmentId("inv1").idType("SecId").useCase("feed").date("2025-01-01").build();
        ResponseEntity<?> response = licenseAuditController.getCellInspection(cellRequest, "token1", "productId", "req1", "", "");
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testGetCellInspectionFail() {
        LicenseCellEntity cellRequest1 = LicenseCellEntity.builder().investmentId("inv1").idType("SecId").useCase("feed").date("2025-01-01").build();
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getCellInspection(cellRequest1, "token1", "productId", "req1", "", ""));

        LicenseCellEntity cellRequest2 = LicenseCellEntity.builder().dataPointId("dp1").idType("SecId").useCase("feed").date("2025-01-01").build();
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getCellInspection(cellRequest2, "token1", "productId", "req1", "", ""));

        LicenseCellEntity cellRequest3 = LicenseCellEntity.builder().dataPointId("dp1").investmentId("inv1").idType("SecId").useCase("feed").date("2025").build();
        Assert.assertThrows(ValidationException.class, () -> licenseAuditController.getCellInspection(cellRequest3, "token1", "productId", "req1", "", ""));
    }
}