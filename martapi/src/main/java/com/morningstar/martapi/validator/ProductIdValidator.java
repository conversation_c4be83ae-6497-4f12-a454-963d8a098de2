package com.morningstar.martapi.validator;

import com.morningstar.dataac.martgateway.core.common.config.CallerIdRegistry;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.springframework.util.StringUtils;

public class ProductIdValidator implements Validator<HeadersAndParams> {

    private final CallerIdRegistry callerIdRegistry;

    public ProductIdValidator(CallerIdRegistry callerIdRegistry) {
        this.callerIdRegistry = callerIdRegistry;
    }

    @Override
    public void validate(HeadersAndParams headersAndParams) throws RuntimeException {
        String productId = headersAndParams.getProductId();
        // product id should present in the header
        if (!StringUtils.hasText(productId)) {
            throw new InvestmentApiValidationException(Status.INVALID_PRODUCT_ID);
        }

        // product id registration should not be empty
        if (callerIdRegistry == null || callerIdRegistry.isEmpty()) {
            Status status = new Status(Status.INVALID_PRODUCT_ID.getCode(), "Product id registration is empty, no product id is supported");
            throw new InvestmentApiValidationException(status);
        }

        // product id should be valid
        if (!callerIdRegistry.hasId(productId)) {
            throw new InvestmentApiValidationException(Status.INVALID_PRODUCT_ID);
        }
    }
}
