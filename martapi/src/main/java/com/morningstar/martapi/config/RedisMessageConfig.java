package com.morningstar.martapi.config;

import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderScanner;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.martapi.service.RedisMessageListener;
import io.lettuce.core.RedisClient;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.lettuce.core.pubsub.api.sync.RedisPubSubCommands;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Component;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Arrays;
import java.util.List;

@Component
public class RedisMessageConfig {

    private final String configUpdateTopic;
    private final String localIdMapUpdateTopic;
    private final String entitlementTopic;
    private final String uimTokenClearCacheTopic;
    private final RedisClient redisClient;
    private final LocalIdMapperCache localIdMapperCache;
    private final EntitlementCacheService entitlementCacheService;
    private final UimTokenService uimTokenService;
    private final DataPointLoaderScanner dataPointLoaderScanner;

    @Inject
    public RedisMessageConfig(@Value(value = "${topic.sync}") String configUpdateTopic,
                              @Value(value = "${topic.localIdMap}") String localIdMapUpdateTopic,
                              @Value(value = "${topic.entitlement}") String entitlementTopic,
                              @Value(value = "${topic.uimTokenClearCache}") String uimTokenClearCacheTopic,
                              @Named("redisClient") RedisClient redisClient,
                              DataPointLoaderScanner dataPointLoaderScanner,
                              LocalIdMapperCache localIdMapperCache,
                              EntitlementCacheService entitlementCacheService,
                              UimTokenService uimTokenService) {
        this.configUpdateTopic = configUpdateTopic;
        this.localIdMapUpdateTopic = localIdMapUpdateTopic;
        this.entitlementTopic = entitlementTopic;
        this.redisClient = redisClient;
        this.dataPointLoaderScanner = dataPointLoaderScanner;
        this.localIdMapperCache = localIdMapperCache;
        this.entitlementCacheService = entitlementCacheService;
        this.uimTokenService = uimTokenService;
        this.uimTokenClearCacheTopic = uimTokenClearCacheTopic;
    }

    @PostConstruct
    public void subscribeTopic() {
        StatefulRedisPubSubConnection<String, String> connection = redisClient.connectPubSub();
        connection.addListener(new RedisMessageListener(dataPointLoaderScanner, localIdMapperCache, entitlementCacheService, uimTokenService));
        RedisPubSubCommands<String, String> sync = connection.sync();
        List<String> topics = Arrays.asList(configUpdateTopic,localIdMapUpdateTopic, entitlementTopic, uimTokenClearCacheTopic);
        topics.forEach(sync::subscribe);
    }

}
