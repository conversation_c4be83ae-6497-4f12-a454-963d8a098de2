@echo off
echo === 完整gRPC配置验证脚本 ===
echo.

echo 检查主应用程序类中的所有gRPC自动配置排除...
set "count=0"

findstr /C:"GrpcServerAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

findstr /C:"GrpcServerFactoryAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

findstr /C:"GrpcServerSecurityAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

findstr /C:"GrpcAdviceAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

findstr /C:"GrpcHealthServiceAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

findstr /C:"GrpcServerMetricAutoConfiguration" src\main\java\com\morningstar\martapi\MartAPIApplication.java >nul
if %errorlevel%==0 set /a count+=1

if %count%==6 (
    echo ✓ 主应用程序类正确排除了所有6个gRPC自动配置类
) else (
    echo ✗ 主应用程序类只排除了%count%个gRPC自动配置类，应该是6个
)

echo.
echo 检查GrpcConfig类的导入配置...
set "import_count=0"

findstr /C:"GrpcServerAutoConfiguration" src\main\java\com\morningstar\martapi\config\GrpcConfig.java >nul
if %errorlevel%==0 set /a import_count+=1

findstr /C:"GrpcAdviceAutoConfiguration" src\main\java\com\morningstar\martapi\config\GrpcConfig.java >nul
if %errorlevel%==0 set /a import_count+=1

findstr /C:"GrpcHealthServiceAutoConfiguration" src\main\java\com\morningstar\martapi\config\GrpcConfig.java >nul
if %errorlevel%==0 set /a import_count+=1

if %import_count%==3 (
    echo ✓ GrpcConfig类正确导入了所有gRPC自动配置类
) else (
    echo ✗ GrpcConfig类导入配置不完整
)

echo.
echo 检查条件注解...
findstr /C:"ConditionalOnProperty" src\main\java\com\morningstar\martapi\grpc\service\TimeSeriesGrpcService.java >nul
if %errorlevel%==0 (
    echo ✓ TimeSeriesGrpcService有正确的条件注解
) else (
    echo ✗ TimeSeriesGrpcService缺少条件注解
)

findstr /C:"ConditionalOnProperty" src\main\java\com\morningstar\martapi\grpc\interceptor\MetadataInterceptor.java >nul
if %errorlevel%==0 (
    echo ✓ MetadataInterceptor有正确的条件注解
) else (
    echo ✗ MetadataInterceptor缺少条件注解
)

echo.
echo 检查application.yml配置...
findstr /C:"enabled: ${ENABLE_GRPC:false}" src\main\resources\application.yml >nul
if %errorlevel%==0 (
    echo ✓ application.yml配置正确（默认禁用gRPC）
) else (
    echo ✗ application.yml配置不正确
)

echo.
echo === 完整配置验证完成 ===
echo.
echo 现在排除的gRPC自动配置类包括：
echo 1. GrpcServerAutoConfiguration - 核心服务器配置
echo 2. GrpcServerFactoryAutoConfiguration - 服务器工厂配置  
echo 3. GrpcServerSecurityAutoConfiguration - 安全配置
echo 4. GrpcAdviceAutoConfiguration - 异常处理配置
echo 5. GrpcHealthServiceAutoConfiguration - 健康检查配置
echo 6. GrpcServerMetricAutoConfiguration - 指标监控配置
echo.
echo 这确保了当ENABLE_GRPC=false时，不会加载任何gRPC相关对象！
echo.
pause
