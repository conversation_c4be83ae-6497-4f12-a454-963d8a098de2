package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.async.entity.GridViewAsyncInput;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.AsyncApiService;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.async.entity.AsyncInput;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@RestController
@RequestMapping(value = {"/v1","/investment-api/v1"})
public class AsyncApiController {

    private final AsyncApiService asyncApiService;
    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator;
    private final Scheduler asyncScheduler;

    private static final Logger log = LoggerFactory.getLogger(AsyncApiController.class);

    @Inject
    public AsyncApiController(
            AsyncApiService asyncApiService,
            @Qualifier("asyncApiValidator") RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator,
            @Qualifier("asyncScheduler") Scheduler asyncScheduler
    ) {
        this.asyncApiService = asyncApiService;
        this.validator = validator;
        this.asyncScheduler = asyncScheduler;
    }

    @PostMapping(value="/async-data")
    public Mono<AsyncApiResponseEntity> fetchAsyncData(
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName,
            @RequestBody InvestmentApiRequest investmentApiRequest
    ) {
        long startTime = System.currentTimeMillis();
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .tsId(tsId)
                .serviceName(serviceName)
                .build();

        String userIdFromToken = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);

        MDC.put("request_id", requestId);
        investmentApiRequest.setRequestId(requestId);
        AsyncInput asyncInput = GridViewAsyncInput.builder()
                .productId(productId)
                .userId(userIdFromToken)
                .configId(configId)
                .investmentApiRequest(investmentApiRequest)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();

        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(investmentApiRequest);
        validator.validateDataEntitlement(userIdFromToken, configId);
        return Mono.fromCallable(() -> asyncApiService.fetchAsyncData(asyncInput))
                .subscribeOn(asyncScheduler)
                .doOnEach(LogHelper.logOnNext(list -> {
                    LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(SERVICE_NAME_CALLER, serviceName),
                            new LogEntity(TS_ID, tsId),
                            new LogEntity(USER_ID, userIdFromToken),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
                }))
                .doOnError(e -> {
                    LogEntry.error(
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, String.format("event_type=\"Async Api error\", event_description=\"Async Api fetch Async Data failed\", error_message=\"%s\"", e.getMessage())),
                            new LogEntity(EXCEPTION_MESSAGE, e),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(SERVICE_NAME_CALLER, serviceName),
                            new LogEntity(TS_ID, tsId),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
                    if (e instanceof InvestmentApiValidationException e1) {
                        throw e1;
                    } else
                        throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
                });

    }

    @GetMapping(value="/async-status/{jobId}")
    public Mono<AsyncApiResponseEntity> getAsyncJobStatus(
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-UserId", required = false, defaultValue = "") String userId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String bearerToken,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName,
            @PathVariable("jobId") String jobId
    ){
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(bearerToken)
                .productId(productId)
                .requestId(requestId)
                .serviceName(serviceName)
                .tsId(tsId)
                .build();
        validator.validateHeadersAndParams(headersAndParams);
        return  Mono.fromCallable(() -> asyncApiService.getGridviewStatus(productId, bearerToken, jobId.toLowerCase(), userId))
                .subscribeOn(asyncScheduler)
                .doOnError(e -> {
                    if (e instanceof InvestmentApiValidationException e1)
                        throw e1;
                    else
                        throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
                });
    }
}
