package com.morningstar.martapi.validator;

import com.morningstar.dataac.martgateway.core.common.config.CallerIdRegistry;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import org.springframework.util.StringUtils;

public class ServiceNameValidator implements Validator<HeadersAndParams> {

    private final CallerIdRegistry serviceNameRegistry;

    public ServiceNameValidator(CallerIdRegistry serviceNameRegistry) {
        this.serviceNameRegistry = serviceNameRegistry;
    }

    @Override
    public void validate(HeadersAndParams headersAndParams) throws RuntimeException {
        String serviceName = headersAndParams.getServiceName();

        if (!StringUtils.hasText(serviceName)) {
            return;
        }

        // service name should be valid
        if (!serviceNameRegistry.hasId(serviceName)) {
            throw new InvestmentApiValidationException(Status.INVALID_SERVICE_NAME);
        }
    }
}
