package com.morningstar.martapi.config;

import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.martapi.service.RedisMessageListener;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderScanner;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class RedisMessageListenerTest {

    private final String mockPattern = "config_";
    private final String mockChannel = "config_update_dev";
    private RedisMessageListener listener;
    @Mock
    private DataPointLoaderScanner dataPointLoaderScanner;
    @Mock
    private LocalIdMapperCache localIdMapperCache;
    @Mock
    private EntitlementCacheService entitlementCacheService;
    @Mock
    private UimTokenService uimTokenService;

    @Before
    public void setUp() {
        listener = new RedisMessageListener(dataPointLoaderScanner, localIdMapperCache, entitlementCacheService, uimTokenService);
    }

    @Test
    public void testDataPointLoaderScanner() {
        listener.message(mockChannel, "mock-message");
        Mockito.verify(dataPointLoaderScanner).loadDataPoints();
    }

    @Test
    public void testOthers() {
        listener.message(mockPattern,mockChannel, "mock-message");
        listener.psubscribed(mockPattern, 2);
        listener.punsubscribed(mockPattern, 1);
        listener.subscribed(mockChannel, 2);
        listener.unsubscribed(mockChannel, 1);
    }

    @Test
    public void testLocalIdUpdate(){
        listener.message("local_id_cache_update", "mock-message");
        Mockito.verify(localIdMapperCache).loadDeltaData();
    }

    @Test
    public void testEntitlementUpdate(){
        listener.message("entitlement_cache_update", "1");
        Mockito.verify(entitlementCacheService, times(1)).refreshDataPackageInfo();
        Mockito.verify(entitlementCacheService, times(1)).refreshPckgMiscInfo();
        Mockito.verify(entitlementCacheService, times(1)).refreshIndexMisc();
    }

    @Test
    public void testUimTokenClearCache(){
        listener.message("uim_token_clear_cache", "Clear UIM Token Cache");
        Mockito.verify(uimTokenService, times(1)).clearLocalCache();
    }

    @Test
    public void testAlreadyRunningChannel() throws InterruptedException {
        doAnswer(invocation -> {
            Thread.sleep(1500L);
            return null;
        }) // first call will trigger uimTokenService
                .doNothing() // second call won't
                .when(uimTokenService).clearLocalCache();

        Thread first = new Thread(() -> listener.message("uim_token_clear_cache", "Clear UIM Token Cache"));
        Thread second = new Thread(() -> listener.message("uim_token_clear_cache", "Clear UIM Token Cache"));

        first.start();
        second.start();

        first.join();
        second.join();

        Mockito.verify(uimTokenService, times(1)).clearLocalCache();
    }
}
