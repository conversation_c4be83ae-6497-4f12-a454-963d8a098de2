package com.morningstar.martapi.controller;

import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martgateway.domains.core.entity.holdingresponse.HoldingResponse;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.MartData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.service.MartGateway;
import java.util.*;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class HoldingDataControllerTest {

    private HoldingDataController holdingDataController;

    MartGateway<HoldingResponse, MartRequest> investmentGateway;

    @Before
    public void setup() {
        investmentGateway = mock(MartGateway.class);
        holdingDataController = new HoldingDataController(investmentGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void getAllData() {
        HoldingResponse holdingResponse = new HoldingResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> data = new ArrayList<>();
        R row = new R("test1");
        data.add(row);
        MartData martData = new MartData(data);
        holdingResponse.setStatus(status);
        Mono<HoldingResponse> holdingResponseMono = Mono.just(holdingResponse);

        when(investmentGateway.asyncRetrieveSecurities(any())).thenReturn(holdingResponseMono);

        Mono<?> response = holdingDataController.getData("123","0P782735", 5, "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                });
    }

    @Test
    public void getAllDataErrorTest() {
        when(investmentGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<?> response = holdingDataController.getData("123","0P782735", 5, "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                });

    }
}