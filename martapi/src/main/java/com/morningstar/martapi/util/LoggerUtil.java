package com.morningstar.martapi.util;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;
import static com.morningstar.martapi.config.Constant.*;

@Component
@NoArgsConstructor()
public class LoggerUtil {
    private static final String REQUEST_TYPE_GRIDVIEW = "gridview";
    private static final String REQUEST_TYPE_HOLDING_DATA = "holding-data";

    public void logAccess(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime) {
        long executeTime = System.currentTimeMillis() - startTime;
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), headersAndParams.getRequestId());
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EXECUTE_TIME, executeTime),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_GRIDVIEW),
                new LogEntity(PRODUCT_ID, headersAndParams.getProductId()),
                new LogEntity(SERVICE_NAME_CALLER, headersAndParams.getServiceName()),
                new LogEntity(TS_ID, headersAndParams.getTsId()),
                new LogEntity(USER_ID, investmentApiRequest.getUserId()),
                new LogEntity("investment_number", investmentApiRequest.getInvestments().size()),
                new LogEntity("datapoint_number", investmentApiRequest.getDataPoints().size())
        ).collect(Collectors.toCollection(ArrayList::new));
        logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(investmentApiRequest)));
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    public void logError(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime, Throwable e) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), headersAndParams.getRequestId());
        LogEntry.error(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, e),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_GRIDVIEW),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(investmentApiRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, e.getClass()),
                new LogEntity(PRODUCT_ID, headersAndParams.getProductId()),
                new LogEntity(SERVICE_NAME_CALLER, headersAndParams.getServiceName()),
                new LogEntity(TS_ID, headersAndParams.getTsId()),
                new LogEntity(USER_ID, investmentApiRequest.getUserId())
        );
    }

    public void logAccess(LicenseAuditEntity licenseAuditEntity, String productId, String requestId, String userId, long startTime, boolean isSummary, String serviceName, String tsId) {
        long executeTime = System.currentTimeMillis() - startTime;
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        String requestType = isSummary ? "licenseSummary" : "licenseAudit";
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EXECUTE_TIME, executeTime),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, requestType),
                new LogEntity(PRODUCT_ID, productId),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId),
                new LogEntity(USER_ID, userId),
                new LogEntity("investment_number", licenseAuditEntity.getInvestments().size()),
                new LogEntity("datapoint_number", licenseAuditEntity.getDatapoints().size())
        ).collect(Collectors.toCollection(ArrayList::new));
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    public void logError(LicenseAuditEntity licenseAuditEntity, String productId, String requestId, String userId, long startTime, boolean isSummary, String serviceName, String tsId, Throwable e) {
        if(!(e instanceof ValidationException)) {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            String requestType = isSummary ? "licenseSummary" : "licenseAudit";
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
                    new LogEntity(REQUEST_TYPE, requestType),
                    new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(licenseAuditEntity)),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(USER_ID, userId)
            );
        }
    }

    public void logAccess(LicenseCellEntity licenseCellEntity, String productId, String requestId, String userId, long startTime, boolean isSummary, String serviceName, String tsId) {
        long executeTime = System.currentTimeMillis() - startTime;
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EXECUTE_TIME, executeTime),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, "licenseCellDetails"),
                new LogEntity(PRODUCT_ID, productId),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId),
                new LogEntity(USER_ID, userId)
        ).collect(Collectors.toCollection(ArrayList::new));
        logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(licenseCellEntity)));
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    public void logError(LicenseCellEntity licenseCellEntity, String productId, String requestId, String userId, long startTime, String serviceName, String tsId, Throwable e) {
        if (!(e instanceof ValidationException)) {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
                    new LogEntity(REQUEST_TYPE, "licenseCellDetails"),
                    new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(licenseCellEntity)),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(USER_ID, userId)
            );
        }
    }
}
