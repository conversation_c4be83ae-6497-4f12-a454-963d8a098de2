package com.morningstar.martapi.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.CurrentPair;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.common.util.LogUtil;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementInvestmentApiRequestWrapper;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.entity.TranscriptApiRequest;
import com.morningstar.martapi.entity.TranscriptApiResponse;
import com.morningstar.martapi.exception.JsonDownloadException;
import com.morningstar.martapi.exception.TranscriptApiException;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.io.ByteArrayInputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.morningstar.dataac.martgateway.core.common.util.LoggerUtil.logTimerEvent;
import static com.morningstar.martapi.config.Constant.*;

@Slf4j
@Service
public class DocumentService {

    @Value("${transcript.url}")
    private String baseUrl;
    private final MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway;
    private final UimTokenService uimTokenService;
    private final WebClient webClient;
    private final DataEntitlementService<InvestmentApiRequest> dataEntitlementService;
    private final RedisReactiveRepo documentUrlCacheRepo;
    private static final String REDIS_KEY_PREFIX = "documentService/";
    private final EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService;
    public DocumentService(MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway, WebClient.Builder builder, UimTokenService uimTokenService, DataEntitlementService<InvestmentApiRequest> dataEntitlementService,EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService, @Qualifier("appCacheRedisClient") RedisReactiveRepo redisReactiveRepo) {
        this.gridViewGateway = gridViewGateway;
        this.uimTokenService = uimTokenService;
        this.webClient = builder.build();
        this.dataEntitlementService = dataEntitlementService;
        this.entitlementInvestmentApiRequestFilterService = entitlementInvestmentApiRequestFilterService;
        this.documentUrlCacheRepo = redisReactiveRepo;
    }

    public Mono<ByteArrayInputStream> getDocument(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime, String documentId) {
        CachedEntitlement cachedEntitlement = null;
        FilteredRequestData<InvestmentApiRequest> filteredRequestData;
        EntitlementInvestmentApiRequestWrapper entitlementInvestmentApiRequestWrapper = new EntitlementInvestmentApiRequestWrapper(investmentApiRequest);
        try {
            cachedEntitlement = dataEntitlementService.getEntitlement(investmentApiRequest.getUserId(), investmentApiRequest.getConfigId());
            if(cachedEntitlement.getCellMatchList().stream().noneMatch(x -> Objects.equals(x.getPackageId(), PACKAGE_ID)))
                throw new EntitlementException(Status.NO_ENTITLEMENT_INFO, "User is not entitled to access the package", investmentApiRequest.getRequestId());
        } catch(EntitlementException e) {
            throw e;
        } catch (Exception e) {
            // if request has valid user/config id, continue process without entitlement in cache
            LogUtil.logWarn(e, investmentApiRequest.getUserId(), "retrieve entitlement info",
                    "entitlement info retrieval failure");
        }
        filteredRequestData = entitlementInvestmentApiRequestFilterService.filterRequest(
                cachedEntitlement, entitlementInvestmentApiRequestWrapper);
        investmentApiRequest = filteredRequestData.getRequest();

        if(investmentApiRequest.getDataPoints() == null || investmentApiRequest.getInvestments() == null || CollectionUtils.isEmpty(investmentApiRequest.getDataPoints()) || CollectionUtils.isEmpty(investmentApiRequest.getInvestments())) {
             return Mono.error(new TranscriptApiException("Investment API request failed - Missing required data", headersAndParams,investmentApiRequest.getUserId(), new NullPointerException("Data from Investment API not found.")));
        }

        StopWatch overallTimer = new StopWatch();
        overallTimer.start("document service");
        InvestmentApiRequestUtil investmentApiRequestUtil = new InvestmentApiRequestUtil();

        Mono<String> jsonData = makePostRequestForTranscriptApi(investmentApiRequestUtil, investmentApiRequest, headersAndParams, startTime, documentId);
        InvestmentApiRequest finalInvestmentApiRequest = investmentApiRequest;
        return jsonData.map(data -> {
                    MDC.put("request_id", finalInvestmentApiRequest.getRequestId());
                    overallTimer.stop();
                    return new ByteArrayInputStream(data.getBytes(StandardCharsets.UTF_8)); // Change this to url, also set TTL
                })
                .doFinally(signalType -> MDC.clear());
    }

    private Mono<String> makePostRequestForTranscriptApi(InvestmentApiRequestUtil investmentApiRequestUtil, InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime, String documentId) {
        Mono<String> tokenMono = Mono.fromCallable(() -> uimTokenService.getUimToken("Document Service"))
                .onErrorMap(e -> new TranscriptApiException("Error fetching token for Transcript API", headersAndParams, investmentApiRequest.getUserId(), e));

        Mono<InvestmentResponse> investmentResponseTranscript = investmentApiRequestUtil.getInvestmentResponse(gridViewGateway, investmentApiRequest, headersAndParams, startTime);

        Mono<Map<String,String>> combinedMono = getDataFromInvestmentResponseTranscript(investmentResponseTranscript);

        // Extract Job ID for Redis Cache Key to obtain Transcript URL
        Mono<String> redisDocumentKey = combinedMono
                .map(map -> String.join("", REDIS_KEY_PREFIX, map.get(JOB_ID_DATAPOINT)))
                .onErrorMap(e -> new IllegalStateException("Could not extract the Job ID from Investment Service Response"));

        if(investmentApiRequest.canReadCache()) {
            Mono<String> url = documentUrlCacheRepo.getString(redisDocumentKey);
            return url
                    .flatMap(this::fetchJsonFromUrl)
                    .switchIfEmpty(getDataFromTranscriptApi(investmentApiRequest, headersAndParams, documentId, combinedMono, tokenMono, redisDocumentKey));
        }

        return getDataFromTranscriptApi(investmentApiRequest, headersAndParams, documentId, combinedMono, tokenMono, redisDocumentKey);
    }

    private Mono<String> getDataFromTranscriptApi(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, String documentId, Mono<Map<String, String>> combinedMono, Mono<String> tokenMono, Mono<String> redisDocumentKey) {
        Mono<TranscriptApiRequest> transcriptApiRequestMono = combinedMono
                .map(dataMap -> {
                    String companyId = dataMap.get(COMPANY_ID_DATAPOINT);
                    String jobId = dataMap.get(JOB_ID_DATAPOINT);
                    String eventDateTime = dataMap.get(EVENT_DATETIME_DATAPOINT);

                    TranscriptApiRequest transcriptApiRequest = new TranscriptApiRequest();
                    transcriptApiRequest.setEntityId(companyId);
                    transcriptApiRequest.setEventDatetime(eventDateTime);
                    transcriptApiRequest.setJobId(jobId);
                    transcriptApiRequest.setEventId(documentId);

                    return transcriptApiRequest;
                });

        Mono<TranscriptApiResponse> transcriptApiResponseMono = tokenMono.zipWith(transcriptApiRequestMono)
                .flatMap(tuple -> {
                    String token = tuple.getT1();
                    TranscriptApiRequest transcriptApiRequest = tuple.getT2();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
                    transcriptApiRequest.setEventDatetime(formatter.withZone(ZoneOffset.UTC).format(Instant.parse(transcriptApiRequest.getEventDatetime())));

                    return webClient.post()
                            .uri(baseUrl)
                            .header("Authorization", token)
                            .bodyValue(transcriptApiRequest)
                            .retrieve()
                            .bodyToMono(TranscriptApiResponse.class);
                })
                .onErrorMap(e -> new TranscriptApiException("Error making POST request to Transcript API", headersAndParams, investmentApiRequest.getUserId(), e));
        return transcriptApiResponseMono.flatMap(transcriptApiResponse -> {
            String url = getTranscriptUrl(transcriptApiResponse);
            documentUrlCacheRepo.setexString(redisDocumentKey, Duration.ofHours(12), url);
            return fetchJsonFromUrl(url);
        });
    }

    private String getTranscriptUrl(TranscriptApiResponse transcriptApiResponse) {
        if (transcriptApiResponse == null) {
            throw new IllegalStateException("TranscriptApiResponse is null");
        }
        if (transcriptApiResponse.getValidatedJsonUrl() != null && !"NOT_FOUND".equals(transcriptApiResponse.getValidatedJsonUrl())) {
            return transcriptApiResponse.getValidatedJsonUrl();
        } else if (transcriptApiResponse.getRawJsonUrl() != null && !"NOT_FOUND".equals(transcriptApiResponse.getRawJsonUrl())) {
            return transcriptApiResponse.getRawJsonUrl();
        } else {
            throw new IllegalStateException("No valid JSON URL found in TranscriptApiResponse");
        }
    }

    private void validateInvestmentResponse(InvestmentResponse e) {
        if (e == null || CollectionUtils.isEmpty(e.getInvestments())) {
            throw new NullPointerException("Data from Investment API not found.");
        }
    }

    private String getCurrentPairValue(Investment investment, String dataPointId) {
        if (CollectionUtils.isEmpty(investment.getCurrentPairList())) {
            throw new IllegalStateException("Data not found for DataPointID " + dataPointId + " " + investment.getErrors());
        }

        return investment.getCurrentPairList().stream()
                .filter(pair -> dataPointId.equalsIgnoreCase(pair.getDatapointId()))
                .findFirst()
                .map(CurrentPair::getValue)
                .orElseThrow(() -> new IllegalStateException("Data not found for DataPointID " + dataPointId));
    }

    Mono<String> fetchJsonFromUrl(String signedUrl) {
        try {
            return webClient.get()
                    .uri(new URL(signedUrl).toURI())
                    .header("Accept", "application/json")
                    .header("User-Agent", "Mozilla/5.0")
                    .retrieve()
                    .onStatus(
                            status -> !status.is2xxSuccessful(),
                            clientResponse -> clientResponse.bodyToMono(String.class)
                                    .flatMap(errorBody -> Mono.error(new JsonDownloadException("Failed to fetch JSON: HTTP error code " + clientResponse.statusCode())))
                    )
                    .bodyToMono(String.class);
        } catch (WebClientResponseException e) {
            throw new JsonDownloadException("Failed to fetch JSON: HTTP error code " + e.getRawStatusCode(), e);
        } catch (Exception e) {
            throw new JsonDownloadException("Failed to fetch JSON", e);
        }
    }

    private Mono<Map<String,String>> getDataFromInvestmentResponseTranscript(Mono<InvestmentResponse> investmentResponseTranscript) {
        Map<String,String> datapointMap = new HashMap<>();
        List<String> dataPointIdNames = List.of(COMPANY_ID_DATAPOINT,JOB_ID_DATAPOINT,EVENT_DATETIME_DATAPOINT);
        return investmentResponseTranscript.map(e -> {
            for (String dataPointId : dataPointIdNames){
                validateInvestmentResponse(e);
                Investment investment = e.getInvestments().get(0);

                if (COMPANY_ID_DATAPOINT.equalsIgnoreCase(dataPointId)) {
                    String investmentId = StringUtils.isEmpty(getCurrentPairValue(investment,dataPointId)) ? investment.getId() : getCurrentPairValue(investment, dataPointId);
                    datapointMap.put(dataPointId,investmentId);
                } else {
                    datapointMap.put(dataPointId,getCurrentPairValue(investment, dataPointId));

                }
            }
            return datapointMap;
        });
    }

}
