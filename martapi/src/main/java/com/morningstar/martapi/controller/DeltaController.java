package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

@RestController
@RequestMapping(value = "/investment-api/v1/delta-detection", produces = MediaType.APPLICATION_JSON_VALUE)
public class DeltaController {

    private final MartGateway<InvestmentResponse, InvestmentApiRequest> deltaDetectionGateway;
    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator;

    public DeltaController(
            @Qualifier("deltaGatewayImpl") MartGateway<InvestmentResponse, InvestmentApiRequest> deltaDetectionGateway,
            @Qualifier("deltaDetectionApiValidator") RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator
    ) {
        this.deltaDetectionGateway = deltaDetectionGateway;
        this.deltaDetectionApiValidator = deltaDetectionApiValidator;
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<InvestmentResponse> getDeltaDetection(
            @RequestBody InvestmentApiRequest investmentApiRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName) {
        long startTime = System.currentTimeMillis();
        investmentApiRequest.setDeltaDetection(true);
        InvestmentApiRequestUtil investmentApiRequestUtil = new InvestmentApiRequestUtil();
        HeadersAndParams headersAndParams = investmentApiRequestUtil.getHeaderAndParams(token, productId, requestId, tsId, serviceName);
        deltaDetectionApiValidator.validateHeadersAndParams(headersAndParams);
        deltaDetectionApiValidator.validateRequestBody(investmentApiRequest);
        return deltaDetectionGateway.asyncRetrieveSecurities(investmentApiRequest).doOnEach(LogHelper.logOnNext(list -> {
            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
        })).doOnEach(LogHelper.logOnError(e -> {
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                    new LogEntity(TS_ID, tsId),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
        }));
    }
}
