package com.morningstar.martapi.controller;

import com.morningstar.martgateway.cloud.ecs.metadata.AwsEcsServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(value = "/")
@RequiredArgsConstructor
public class HealthCheckController {

    private static final String RESPONSE_TEMPLATE = "System is healthy. Environment: %s, B/G env: %s, Application: %s";
    private static final String BG_DEFAULT_VALUE = " (Default value)";
    private static final String BG_OPTION_TURNED_OFF = "(option not turned on)";

    private final AwsEcsServiceClient awsEcsServiceClient;

    @Value("${spring.profiles}")
    private String env;

    @Value("${martgateway.application}")
    private String application;

    @Value("${martcommon.aws.s3.default-env}")
    private String bgDefaultEnv;

    @GetMapping(value = {"healthCheck", "v1/health-check", "investment-api/v1/health-check"})
    public ResponseEntity<String> checkHealth(@RequestParam(defaultValue = "false", required = false) boolean includeBgInfo) {
        return provideResponse(includeBgInfo, application);
    }

    @GetMapping(value = {"v1/time-series-data/health-check", "investment-api/v1/time-series-data/health-check"})
    public ResponseEntity<String> checkTimeSeriesClusterHealth(@RequestParam(defaultValue = "false", required = false) boolean includeBgInfo) {
        return provideResponse(includeBgInfo, application);
    }

    private ResponseEntity<String> provideResponse(boolean includeBgInfo, String applicationName) {
        String bgEnv = includeBgInfo ?
                awsEcsServiceClient.getBlueGreenOrDefault(bgDefaultEnv + BG_DEFAULT_VALUE) :
                BG_OPTION_TURNED_OFF;

        return new ResponseEntity<>(String.format(RESPONSE_TEMPLATE, env, bgEnv, applicationName), HttpStatus.OK);
    }
}
