package com.morningstar.martapi.config;

import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;

import javax.inject.Named;

@Named
public class NettyWebServerFactoryHeaderSizeCustomizer implements WebServerFactoryCustomizer<NettyReactiveWebServerFactory> {
    @Override
    public void customize(NettyReactiveWebServerFactory container) {
        container.addServerCustomizers(builder -> builder.httpRequestDecoder(
                httpRequestDecoderSpec ->
                        httpRequestDecoderSpec.maxInitialLineLength(49152)
        ));
    }
}
