package com.morningstar.martapi.controller;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martgateway.domains.core.entity.response.MartData;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
@RunWith(MockitoJUnitRunner.class)
public class SecurityControllerTest {

    private MartGateway martGateway;
    private SecurityController controller;

    @Before
    public void setUp() {
        martGateway = mock(MartGateway.class);
        controller = new SecurityController(martGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void getDatapointValuesTest() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(martResponseMonoT);
        Mono<MartResponse> martResponseMono = controller.getDatapointValues("0P00009T69,F00000ONLR", "90324,90325", "2020-07-01", "2020-07-20", "USD", null, null, "false", "0", "", "", "", "", "", "","", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getDatapointValuesTest2() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return  martResponseMonoT;
        });
        Mono<MartResponse> martResponseMono = controller.getDatapointValues("0P00009T69,F00000ONLR", "90324,90325", "2020-07-01", "2020-07-20", "USD", null, null, "false", "0", "", "", "","",  "", "","", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getDataPointsForAsOfDate() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        MartRequest martRequest = mock(MartRequest.class);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(martResponseMonoT);
        Mono<MartResponse> martResponseMono = controller.getDataPointsForAsOfDate(martRequest, MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getDataPointsForAsOfDate2() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        MartRequest martRequest = mock(MartRequest.class);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return martResponseMonoT;
        });
        Mono<MartResponse> martResponseMono = controller.getDataPointsForAsOfDate(martRequest, MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getDatapointValuesErrorTest() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<MartResponse> martResponseMono = controller.getDatapointValues("0P00009T69,F00000ONLR", "90324,90325", "2020-07-01", "2020-07-20", "USD", null, null, "false", "0", "", "", "", "", "","","", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getDataPointsForAsOfDateLogError() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        MartRequest martRequest = mock(MartRequest.class);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<MartResponse> martResponseMono = controller.getDataPointsForAsOfDate(martRequest, MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getCategoryDatapointValuesTest() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.OK;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(martResponseMonoT);
        Mono<MartResponse> martResponseMono = controller.getCategoryDatapointValues("53118","1","1","BRCA000020", "", "", "","", "false","0", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getCategoryDatapointValuesTest2() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.OK;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return martResponseMonoT;
        });
        Mono<MartResponse> martResponseMono = controller.getCategoryDatapointValues("53118","1","1","BRCA000020", "", "", "", "","false","0", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }

    @Test
    public void getCategoryDatapointValuesTestError() {
        MartResponse martResponse = new MartResponse();
        Status status = Status.INTERNAL_ERROR;
        List<R> response = new ArrayList<>();
        R r = new R("test1");
        response.add(r);
        MartData martData = new MartData(response);
        martResponse.setStatus(status);
        martResponse.setData(martData);
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(martGateway.asyncRetrieveSecurities(any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<MartResponse> martResponseMono = controller.getCategoryDatapointValues("53118","1","1","BRCA000020", "", "", "", "", "false","0", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }


}
