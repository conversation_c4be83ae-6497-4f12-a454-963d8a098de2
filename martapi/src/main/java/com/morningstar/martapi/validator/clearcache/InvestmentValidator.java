package com.morningstar.martapi.validator.clearcache;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.ClearCacheRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class InvestmentValidator implements Validator<ClearCacheRequest> {

    @Override
    public void validate(ClearCacheRequest request) throws ValidationException {
        List<String> investments = request.getInvestmentIds();
        List<String> invalidIds = investments == null ? investments : investments.stream().filter(StringUtils::isEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(investments) || CollectionUtils.isNotEmpty(invalidIds)) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - investmentIds");
            throw new ValidationException(status);
        }
    }
}