package com.morningstar.martapi.validator;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;


public class AuthTokenValidator implements Validator<HeadersAndParams> {

    private final String tokenIssuer;

    public AuthTokenValidator(String tokenIssuer) {
        this.tokenIssuer = tokenIssuer;
    }

    @Override
    public void validate(HeadersAndParams headersAndParams) throws InvestmentApiValidationException {
        String token = headersAndParams.getAuthorizationToken();
        try {
            if (StringUtils.startsWith(token, "Bearer ")) {
                token = token.substring(7);
            }
            DecodedJWT decodedToken = JWT.decode(token);
            String issuer = decodedToken.getIssuer();

//            if (!issuer.contains(tokenIssuer)) {
//                throw new InvestmentApiValidationException(Status.INVALID_TOKEN.withMessage("User provided invalid token"));
//            }

            if (isExpiredToken(decodedToken)) {
                throw new InvestmentApiValidationException(Status.EXPIRED_TOKEN);
            }

            if (StringUtils.isEmpty(decodedToken.getClaim("https://morningstar.com/mstar_id").asString()) &&
                    StringUtils.isEmpty(decodedToken.getClaim("https://morningstar.com/config_id").asString())) {
                throw new InvestmentApiValidationException(Status.INVALID_TOKEN);
            }
        } catch (JWTDecodeException e) {
            throw new InvestmentApiValidationException(Status.INVALID_TOKEN);
        }
    }

    private boolean isExpiredToken(DecodedJWT token) {
        Date tokenExpireDate = token.getExpiresAt();
        return tokenExpireDate.before(new Date());
    }
}
