package com.morningstar.martapi.controller;

import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.interfaces.DeltaGatewayImpl;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@ExtendWith(MockitoExtension.class)
public class DeltaControllerTest {

    @Mock
    private DeltaGatewayImpl deltaGateway;

    private DeltaController deltaController;

    @BeforeEach
    public void setup() {
        deltaController = new DeltaController(deltaGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void getDeltaDetection() {
        Mockito.when(deltaGateway.asyncRetrieveSecurities(ArgumentMatchers.any())).thenReturn(Mono.just(new InvestmentResponse()));
        Mono<InvestmentResponse> responseMono = deltaController.getDeltaDetection(buildRequest(), "jwt_token", UUID.randomUUID().toString(), "MDS", "", "");
        StepVerifier.create(responseMono)
                .assertNext(Assertions::assertNotNull)
                .verifyComplete();

    }

    private InvestmentApiRequest buildRequest() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now())
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

}
