package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;

public class TsMartRequestDataPointValidator implements Validator<MartRequest> {

@Override
public void validate(MartRequest request) throws ValidationException {
		List<String> dataPoints = request.getDps();
		if (CollectionUtils.isEmpty(dataPoints)) {
			Status status = Status.MISSING_ATTRIBUTE
			.withMessage("Request input missing mandatory attribute - dataPoints");
			throw new ValidationException(status);
		}

		Set<String> distinctDps = new HashSet<>();
		for (String dps : dataPoints) {
			if (!distinctDps.add(dps)) {
				Status status = Status.DUPLICATE_DATAPOINT;
				throw new ValidationException(status);
			}
		}
	}
}
