package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

public class TsMartRequestInvestmentValidatorTest {
	private TsMartRequestInvestmentValidator validator;

	@Before
	public void setup() {
		this.validator = new TsMartRequestInvestmentValidator();
	}

	@Test
	public void validate() {
		MartRequest request1 = MartRequest.builder()
				.useCase("view")
				.ids(List.of("", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request1));
		Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());

		MartRequest request2 = MartRequest.builder()
				.useCase("view")
				.dps(List.of("HP010", "HP022"))
				.build();
		exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request2));
		Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
	}

	@Test
	public void validateDuplicateInvestment() {
		MartRequest request1 = MartRequest.builder()
				.useCase("view")
				.ids(List.of("OP01012", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request1));
		Assertions.assertEquals(Status.DUPLICATE_INVESTMENT.getCode(),exception.getStatus().getCode());
	}

	@Test
	public void validateCorrectInput() {
		MartRequest request1 = MartRequest.builder()
				.useCase("view")
				.ids(List.of("OP01012", "OP01014"))
				.dps(List.of("HP010", "HP022"))
				.build();
		Assertions.assertDoesNotThrow(() -> validator.validate(request1));
	}
}
