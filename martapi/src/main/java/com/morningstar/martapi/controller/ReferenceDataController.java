package com.morningstar.martapi.controller;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.SERVICE_NAME_CALLER;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.TS_ID;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.USER_ID;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.service.log.LogHelper;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martapi.exception.IndexAPIException;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataResult;
import com.morningstar.martgateway.domains.index.entity.IndexDataResult;
import com.morningstar.martgateway.domains.index.service.IndexDataService;
import io.swagger.annotations.ApiParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.util.Logger;
import reactor.util.Loggers;

@RestController
@RequestMapping(value = {"/v1/reference-data/","/investment-api/v1/reference-data/"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class ReferenceDataController {

    private final MartGateway<EconomicDataResult, EconomicDataRequest> economicDataGateway;
    private final IndexDataService indexDataService;
    private final RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator;
    private final RequestValidationHandler<HeadersAndParams, EconomicDataRequest> economicValidator;
    private static final Logger log = Loggers.getLogger(ReferenceDataController.class);

    private static final String REQUEST_TYPE_VALUE = "reference-data-api";

    @Inject
    public ReferenceDataController(IndexDataService indexDataService, @Qualifier("investmentApiValidator") RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator, @Qualifier("economicDataGatewayImpl") MartGateway economicDataGateway,@Qualifier("economicManadatoryDataValidator") RequestValidationHandler<HeadersAndParams, EconomicDataRequest> economicValidator) {
        this.indexDataService = indexDataService;
        this.validator = validator;
        this.economicDataGateway = economicDataGateway;
        this.economicValidator=economicValidator;
    }

    @GetMapping(value = "/module-id/{moduleId}")
    public Mono<IndexDataResult> getReferenceIndexData(
            @ApiParam(example = "BTWS000001")
            @PathVariable("moduleId") String moduleId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token, ServerHttpRequest request,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName
    ) {

        HeadersAndParams headersAndParams = new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId, tsId, serviceName);
        String userIdFromToken = TokenUtil.getUserId(token);
        validator.validateHeadersAndParams(headersAndParams);

        return Mono.defer(() -> {
            long startTime = System.currentTimeMillis();
            return indexDataService.getData(moduleId)
                    .doOnSuccess(data -> {
                        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
                        try {
                            LogEntry.info(
                                    new LogEntity(EVENT_TYPE, RESPONSE),
                                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                                    new LogEntity(PRODUCT_ID, productId),
                                    new LogEntity(USER_ID, userIdFromToken),
                                    new LogEntity(REQUEST_PARAM, request.getURI().getPath()),
                                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                                    new LogEntity(TS_ID, tsId),
                                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime)
                            );
                        } finally {
                            MDC.clear();
                        }
                    })
                    .doOnError(e -> {
                        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
                        try {
                            LogEntry.error(
                                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                                    new LogEntity(EVENT_DESCRIPTION, e.getMessage()),
                                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                                    new LogEntity(EXCEPTION_TYPE, e.getClass().getName()),
                                    new LogEntity(PRODUCT_ID, productId),
                                    new LogEntity(USER_ID, userIdFromToken),
                                    new LogEntity(SERVICE_NAME_CALLER, serviceName),
                                    new LogEntity(TS_ID, tsId),
                                    new LogEntity(REQUEST_PARAM, request.getURI().getPath())
                            );
                        } finally {
                            MDC.clear();
                        }
                    })
                    .onErrorMap(e -> new IndexAPIException(e.getMessage()));
        });
    }

    @GetMapping(value = "/economic-indicators")
    public Mono<EconomicDataResult> getReferenceEconomicData(
            @RequestParam(value = "substring", required = false) String substring,
            @RequestParam(value = "indicatorId", required = false) String indicatorId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token, ServerHttpRequest request,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName
    ) {

        HeadersAndParams headersAndParams = new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId, tsId, serviceName);
        validator.validateHeadersAndParams(headersAndParams);

        EconomicDataRequest.EconomicDataRequestBuilder builder = EconomicDataRequest.builder();

        if (!StringUtils.isEmpty(substring)) {
            builder.economicDataFilter(substring);
        }
        if (!StringUtils.isEmpty(indicatorId)) {
            builder.indicatorId(indicatorId);
        }
        EconomicDataRequest economicDataRequest = builder.build();

        economicValidator.validateRequestBody(economicDataRequest);

        long startTime = System.currentTimeMillis();

        return economicDataGateway.asyncRetrieveSecurities(economicDataRequest).doOnEach(LogHelper.logOnNext(list -> {
            List<LogEntity> logEntities = logAccess(economicDataRequest, request, startTime, requestId, tsId, serviceName);
            LogEntry.info(logEntities.toArray(LogEntity[]::new));
        })).doOnEach(LogHelper.logOnError(e -> {
            List<LogEntity> logEntities = logError(economicDataRequest, request, startTime, requestId, e, tsId, serviceName);
            LogEntry.error(logEntities.toArray(LogEntity[]::new));
        }));
    }

    private List<LogEntity> logAccess(
            EconomicDataRequest economicDataRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            String tsId,
            String serviceName
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        long executionTime = System.currentTimeMillis() - startTime;
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PARAM, economicDataRequest.getEconomicDataFilter()),
                new LogEntity(EXECUTE_TIME, executionTime),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId)
        ).collect(Collectors.toCollection(ArrayList::new));
        if (Objects.requireNonNull(request.getMethod()).matches(HttpMethod.POST.name())) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(economicDataRequest)));
        }
        return logEntities;
    }

    private List<LogEntity> logError(
            EconomicDataRequest economicDataRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            Throwable e,
            String tsId,
            String serviceName
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        return Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, e),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(economicDataRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, e.getClass()),
                new LogEntity(REQUEST_PARAM, economicDataRequest.getEconomicDataFilter()),
                new LogEntity(SERVICE_NAME_CALLER, serviceName),
                new LogEntity(TS_ID, tsId)
        ).collect(Collectors.toCollection(ArrayList::new));
    }
}