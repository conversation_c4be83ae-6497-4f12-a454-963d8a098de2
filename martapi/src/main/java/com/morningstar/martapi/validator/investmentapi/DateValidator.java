package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.AbstractDateValidator;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;

public class DateValidator extends AbstractDateValidator implements Validator<InvestmentApiRequest> {

    public DateValidator() {
        super("yyyy-MM-dd");
    }

    @Override
    public void validate(InvestmentApiRequest request) throws RuntimeException {
        for (GridviewDataPoint dataPoint : request.getDataPoints()) {
            if (isWithoutDateRange(dataPoint.getStartDate(), dataPoint.getEndDate())) {
                continue;
            }
            if (isMissingDate(dataPoint.getStartDate(), dataPoint.getEndDate()) || isInvalidDateOrder(dataPoint.getStartDate(), dataPoint.getEndDate())) {
                throw new InvestmentApiValidationException(Status.INVALID_DATE_SETTING);
            }
        }
    }
}
