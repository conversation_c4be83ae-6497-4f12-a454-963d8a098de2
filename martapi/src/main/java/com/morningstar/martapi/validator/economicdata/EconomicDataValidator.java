package com.morningstar.martapi.validator.economicdata;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import org.apache.commons.lang3.StringUtils;


public class EconomicDataValidator implements Validator<EconomicDataRequest> {

    @Override
    public void validate(EconomicDataRequest economicDataRequest) throws RuntimeException {
        if (StringUtils.isEmpty(economicDataRequest.getEconomicDataFilter()) && StringUtils.isEmpty(economicDataRequest.getIndicatorId())) {
            throw new InvestmentApiValidationException(Status.MISSING_ATTRIBUTE);
        }
    }
}