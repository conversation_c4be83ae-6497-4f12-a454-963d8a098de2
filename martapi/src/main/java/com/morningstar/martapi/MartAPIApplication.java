package com.morningstar.martapi;

import com.morningstar.martgateway.util.ExcludeFromTests;
import net.devh.boot.grpc.server.autoconfigure.GrpcAdviceAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerMetricAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerSecurityAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
@ExcludeFromTests
@SpringBootApplication(exclude = {
    MongoAutoConfiguration.class,
    GrpcServerAutoConfiguration.class,
    GrpcServerFactoryAutoConfiguration.class,
    GrpcServerSecurityAutoConfiguration.class,
    GrpcAdviceAutoConfiguration.class,
    GrpcHealthServiceAutoConfiguration.class,
    GrpcServerMetricAutoConfiguration.class
})
@ComponentScan(
        {
                "com.morningstar.martgateway",
                "com.morningstar.martapi"
        })
@MapperScan("com.morningstar.martgateway.infrastructures.repo.data")
@EnableScheduling
public class MartAPIApplication {
    public static void main(String[] args) {
        //enable reactor debug mode
        //remove buffer setting
        SpringApplication.run(MartAPIApplication.class, args);
    }
}

