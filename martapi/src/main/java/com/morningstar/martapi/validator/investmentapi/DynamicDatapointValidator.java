package com.morningstar.martapi.validator.investmentapi;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;

public class DynamicDatapointValidator implements Validator<InvestmentApiRequest> {


    @Override
    public void validate(InvestmentApiRequest content) {
        Map<String, List<String>> groupedErrors = new HashMap<>();

        if (CollectionUtils.isEmpty(content.getDataPoints())) {
            return;
        }

        for (GridviewDataPoint dp : content.getDataPoints()) {
            validateDataPoint(dp, groupedErrors);
        }

        if (!groupedErrors.isEmpty()) {
            String finalMessage = groupedErrors.entrySet().stream()
                    .map(entry -> entry.getKey() + ": " + String.join(", ", entry.getValue()))
                    .collect(Collectors.joining(", "));
            throw new InvestmentApiValidationException(Status.INVALID_DATAPOINT.withMessage(finalMessage));
        }
    }

    private void validateDataPoint(GridviewDataPoint dp, Map<String, List<String>> groupedErrors) {
        List<String> datapointIds = dp.getDataPointIds();
        if (CollectionUtils.isEmpty(datapointIds)) {
            return;
        }

        for (String id : datapointIds) {
            DataPoint datapoint = DataPointRepository.getByNid(id);
            if (datapoint == null) {
                continue;
            }

            Map<String, List<String>> paramsMap = datapoint.getDatapointToParams();
            if (paramsMap == null || !paramsMap.containsKey(datapoint.getId())) {
                continue;
            }

            List<String> requiredParams = paramsMap.get(datapoint.getId());
            if (CollectionUtils.isEmpty(requiredParams)) {
                continue;
            }

            checkRequiredParams(dp, id, requiredParams, groupedErrors);
        }
    }

    private void checkRequiredParams(GridviewDataPoint dp, String datapointId, List<String> requiredParams, Map<String, List<String>> groupedErrors) {
        for (String param : requiredParams) {
            String errorKey = switch (param) {
                case "euTaxonomyObjective" -> isEmpty(dp.getEuTaxonomyObjective()) ? "Missing 'euTaxonomyObjective' for datapointId" : null;
                case "euTaxonomyActivityClusterType" -> isEmpty(dp.getEuTaxonomyActivityClusterType()) ? "Missing 'euTaxonomyActivityClusterType' for datapointId" : null;
                case "euTaxonomyNonNGObjective" -> isEmpty(dp.getEuTaxonomyNonNGObjective()) ? "Missing 'euTaxonomyNonNGObjective' for datapointId" : null;
                case "distributionType" -> isEmpty(dp.getDistributionType()) ? "Missing 'distributionType' for datapointId" : null;
                case "specialDistribution" -> isEmpty(dp.getSpecialDistribution()) ? "Missing 'specialDistribution' for datapointId" : null;
                default -> "Unknown required parameter '" + param + "' for datapointId";
            };

            if (errorKey != null) {
                groupedErrors.computeIfAbsent(errorKey, k -> new ArrayList<>()).add(datapointId);
            }
        }
    }
}
