package com.morningstar.martapi.service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@Component
public class S3PreSignerProvider {

    private final Map<String, S3Presigner> signers = new ConcurrentHashMap<>();

    // Lazily create one per region as the creation is expensive
    public S3Presigner getS3Presigner(String region) {
        return signers.computeIfAbsent(region, r -> S3Presigner
                .builder()
                .credentialsProvider(DefaultCredentialsProvider.create())
                .region(Region.of(r))
                .build());
    }
}
