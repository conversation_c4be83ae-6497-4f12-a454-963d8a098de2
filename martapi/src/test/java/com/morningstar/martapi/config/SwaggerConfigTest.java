package com.morningstar.martapi.config;

import com.fasterxml.classmate.TypeResolver;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import springfox.documentation.spring.web.plugins.Docket;
@RunWith(MockitoJUnitRunner.class)
public class SwaggerConfigTest {
  @Test
  public void api() {
    SwaggerConfig swaggerConfig = new SwaggerConfig();
    TypeResolver typeResolver = new TypeResolver();
    Docket docket = swaggerConfig.api(typeResolver);
    Assert.assertEquals("swagger:2.0", docket.getDocumentationType().toString());
  }
}
