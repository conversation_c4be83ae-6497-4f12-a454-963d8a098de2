package com.morningstar.martapi.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.config.CallerIdRegistry;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ProductIdValidatorTest {

    @Mock
    CallerIdRegistry productIdsRegistry;

    private static final String PRODUCT_ID = "someProductId";

    @Test
    @DisplayName("should not throw error with valid product id")
    public void testValidateHasValue() {
        ProductIdValidator productIdValidator = new ProductIdValidator(productIdsRegistry);
        HeadersAndParams headersAndParams = HeadersAndParams.builder().productId(PRODUCT_ID).build();

        when(productIdsRegistry.isEmpty()).thenReturn(false);
        when(productIdsRegistry.hasId(PRODUCT_ID)).thenReturn(true);

        assertDoesNotThrow(() -> productIdValidator.validate(headersAndParams));
    }

    @Test
    @DisplayName("should throw error without product id")
    public void testNoProductId() {
        ProductIdValidator productIdValidator = new ProductIdValidator(productIdsRegistry);
        HeadersAndParams headersAndParams = HeadersAndParams.builder().build();

        InvestmentApiValidationException exception = assertThrows(
                InvestmentApiValidationException.class, () -> productIdValidator.validate(headersAndParams));
        assertEquals(Status.INVALID_PRODUCT_ID, exception.getStatus());
    }

    @Test
    @DisplayName("should throw error when product is invalid")
    public void testInvalidProductId() {
        ProductIdValidator productIdValidator = new ProductIdValidator(productIdsRegistry);

        when(productIdsRegistry.isEmpty()).thenReturn(false);
        when(productIdsRegistry.hasId(PRODUCT_ID)).thenReturn(false);

        HeadersAndParams headersAndParams = HeadersAndParams.builder().productId(PRODUCT_ID).build();

        InvestmentApiValidationException exception = assertThrows(
                InvestmentApiValidationException.class, () -> productIdValidator.validate(headersAndParams));
        assertEquals(Status.INVALID_PRODUCT_ID, exception.getStatus());
    }

    @Test
    @DisplayName("should throw error without product ids registration")
    public void testEmptyProductIdsRegistration() {
        ProductIdValidator productIdValidator = new ProductIdValidator(productIdsRegistry);
        HeadersAndParams headersAndParams = HeadersAndParams.builder().productId(PRODUCT_ID).build();

        when(productIdsRegistry.isEmpty()).thenReturn(true);

        InvestmentApiValidationException exception = assertThrows(
                InvestmentApiValidationException.class, () -> productIdValidator.validate(headersAndParams));
        assertEquals(Status.INVALID_PRODUCT_ID.getCode(), exception.getStatus().getCode());
        assertTrue(exception.getMessage().contains("no product id is supported"));
    }
}