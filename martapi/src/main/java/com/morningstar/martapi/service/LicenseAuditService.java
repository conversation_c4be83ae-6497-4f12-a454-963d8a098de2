package com.morningstar.martapi.service;

import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageEntitlementResponse;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.Set;

@Named
public class LicenseAuditService {
    private static final Logger log = LoggerFactory.getLogger(LicenseAuditService.class);
    private DataEntitlementService dataEntitlementService;
    private static final Set<String> VALID_USE_CASE = Set.of("feed", "view", "export");
    @Inject
    public LicenseAuditService(DataEntitlementService dataEntitlementService) {
        this.dataEntitlementService = dataEntitlementService;
    }

    public LicenseAuditResponse processAudit(String userId, String productId, String configId, LicenseAuditEntity request, boolean isSummary) {
        PackageEntitlementResponse packageResponse = dataEntitlementService.getLicenseAudit(userId, productId, configId, new ArrayList<>(request.getDatapoints()), new ArrayList<>(request.getInvestments()), isSummary);
        LicenseAuditResponse response = new LicenseAuditResponse();
        response.setDataPackages(new ArrayList<>(packageResponse.getPckDetail().values()));
        if (!CollectionUtils.isEmpty(packageResponse.getUnidentifiedDps())) {
            response.setUnidentifiedDataPoints(packageResponse.getUnidentifiedDps());
        }
        if (!CollectionUtils.isEmpty(packageResponse.getUnidentifiedInvestments())) {
            response.setUnidentifiedInvestments(packageResponse.getUnidentifiedInvestments());
        }
        return response;
    }

    public LicenseCellResponse processCellInspection(String userId, String configId, String productId, LicenseCellEntity request) {
        //Use feed for an invalid request usecase
        String useCase = StringUtils.isEmpty(request.getUseCase()) || !VALID_USE_CASE.contains(request.getUseCase()) ? "feed" : request.getUseCase();
        LicenseCellResponse response = dataEntitlementService.getLicenseCellInspection(userId, configId, productId, request.getDataPointId(), request.getInvestmentId(), request.getDate(), useCase);
        return response;
    }
}
