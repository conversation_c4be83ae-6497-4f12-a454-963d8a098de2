package com.morningstar.martapi.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.service.LicenseAuditService;
import com.morningstar.martapi.util.LoggerUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import java.util.UUID;

@RestController
@RequestMapping(value = {"/v1"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class LicenseAuditController {
    private LicenseAuditService licenseAuditService;
    private final RequestValidationHandler<HeadersAndParams,LicenseAuditEntity> licenseAuditValidator;
    private final RequestValidationHandler<HeadersAndParams,LicenseCellEntity> licenseCellValidator;

    private static final Logger log = LoggerFactory.getLogger(LicenseAuditController.class);

    @Inject
    public LicenseAuditController(LicenseAuditService licenseAuditService, @Qualifier("licenseAuditValidator")RequestValidationHandler<HeadersAndParams,LicenseAuditEntity> licenseAuditValidator, @Qualifier("licenseCellValidator")RequestValidationHandler<HeadersAndParams,LicenseCellEntity> licenseCellValidator) {
        this.licenseAuditService = licenseAuditService;
        this.licenseAuditValidator = licenseAuditValidator;
        this.licenseCellValidator = licenseCellValidator;
    }

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = "license-audit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAudit(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        try {
           validateLicenseAuditRequest(auditRequest, token, productId, requestId, tsId, serviceName);
            MDC.put("request_id", requestId);
            log.info("event_type=\"License Audit API\", event_description=\"Submit Audit request\", user_id=\"{}\", request_id=\"{}\"", userId, requestId);
            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, false);
            loggerUtil.logAccess(auditRequest, productId, requestId, userId, startTime, false, serviceName, tsId);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(auditRequest, productId, requestId, userId, startTime, false, serviceName, tsId, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = "license-audit-summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAuditSummary(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        try {
            validateLicenseAuditRequest(auditRequest, token, productId, requestId, tsId, serviceName);
            MDC.put("request_id", requestId);
            log.info("event_type=\"License Audit API\", event_description=\"Submit Summary request\", user_id=\"{}\", request_id=\"{}\"", userId, requestId);
            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, true);
            loggerUtil.logAccess(auditRequest, "", requestId, userId, startTime, true, serviceName, tsId);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(auditRequest, "", requestId, userId, startTime, true, serviceName, tsId, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }

    @PostMapping(value = "license-audit-details", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getCellInspection(
            @RequestBody LicenseCellEntity cellRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TsId", required = false, defaultValue = "") String tsId,
            @RequestHeader(value = "X-API-ServiceName", required = false, defaultValue = "") String serviceName
            ) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        try {
            validateLicenseCellRequest(cellRequest, token, productId, requestId, tsId, serviceName);
            MDC.put("request_id", requestId);
            log.info("event_type=\"License Cell Inspection API\", event_description=\"Submit License cell inspection request\", user_id=\"{}\", request_id=\"{}\"", userId, requestId);
            LicenseCellResponse response = licenseAuditService.processCellInspection(userId, configId, productId, cellRequest);
            loggerUtil.logAccess(cellRequest, productId, requestId, userId, startTime, true, serviceName, tsId);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(cellRequest, productId, requestId, userId, startTime, serviceName, tsId, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }
    private void validateLicenseAuditRequest(LicenseAuditEntity licenseAuditEntity, String token, String productId, String requestId, String tsId, String serviceName) {
        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .tsId(tsId)
                .serviceName(serviceName)
                .build();
        licenseAuditValidator.validateHeadersAndParams(headersAndParams);
        licenseAuditValidator.validateRequestBody(licenseAuditEntity);
    }
    private void validateLicenseCellRequest(LicenseCellEntity licenseCellEntity, String token, String productId, String requestId, String tsId, String serviceName) {
        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .serviceName(serviceName)
                .tsId(tsId)
                .build();
        licenseCellValidator.validateHeadersAndParams(headersAndParams);
        licenseCellValidator.validateRequestBody(licenseCellEntity);
    }
}
