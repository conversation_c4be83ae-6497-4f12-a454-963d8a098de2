package com.morningstar.martapi.grpc.interceptor;

import io.grpc.Context;
import io.grpc.Contexts;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

@GrpcGlobalServerInterceptor
@ConditionalOnProperty(
    name = "grpc.server.enabled",
    havingValue = "true",
    matchIfMissing = false
)
public class MetadataInterceptor implements ServerInterceptor {


    public static final Context.Key<Metadata> METADATA_KEY = Context.key("metadata");

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call,
            Metadata metadata,
            ServerCallHandler<ReqT, RespT> next) {

        Context context = Context.current().withValue(METADATA_KEY, metadata);
        return Contexts.interceptCall(context, call, metadata, next);
    }
}

