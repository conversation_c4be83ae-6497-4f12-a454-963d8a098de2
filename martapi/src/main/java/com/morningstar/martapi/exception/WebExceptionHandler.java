package com.morningstar.martapi.exception;


import com.morningstar.martgateway.domains.core.entity.InvalidParamException;
import com.morningstar.martgateway.domains.core.entity.MartException;
import com.morningstar.martgateway.domains.core.entity.response.MartData;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import static com.morningstar.martapi.config.Constant.X_API_PRODUCT_ID;
import static com.morningstar.martapi.config.Constant.X_API_REQUEST_ID;
import static com.morningstar.martapi.config.Constant.X_API_SERVICE_NAME;
import static com.morningstar.martapi.config.Constant.X_API_TS_ID;

@ControllerAdvice
public class WebExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebExceptionHandler.class);
    @ExceptionHandler(value = InvalidParamException.class)
    @ResponseBody
    public Mono<MartResponse> handleBadRequest(
            Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        String serviceName  = extractServiceName(ex);
        String tsId  = extractTsId(ex);
        LOGGER.warn("event_type=\"Parameter invalid\", " +
                "event_description=\"API got invalid parameter\", " +
                "url=\"{}\", e=\"{}\", product_id=\"{}\", service_name=\"{}\", ts_id=\"{}\"", url, e.getMessage(), productId, serviceName, tsId);

        Status responseStatus = Status.INVALID_PARAMETER;
        responseStatus.setMessage(e.getMessage());
        return Mono.just(new MartResponse(responseStatus,
                new MartData(new ArrayList<>())));
    }

    @ExceptionHandler(value = MartException.class)
    @ResponseBody
    public Mono<MartResponse> handleFailedDataAccess(
            Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        String serviceName  = extractServiceName(ex);
        String tsId  = extractTsId(ex);
        LOGGER.error("event_type=\"Data Access Errors\", " +
                "event_description=\"failed to access data\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\", service_name=\"{}\", ts_id=\"{}\"", url, productId, e.getMessage(), serviceName, tsId);

        Status responseStatus = Status.INTERNAL_ERROR;
        responseStatus.setMessage(e.getMessage());
        return Mono.just(new MartResponse(responseStatus,
                new MartData(new ArrayList<>())));
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Mono<MartResponse> handleUnExceptedExceptions(
            Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        String requestId = extractRequestId(ex);
        String serviceName  = extractServiceName(ex);
        String tsId  = extractTsId(ex);

        String err = String.format("event_type=\"catch exception\", event_description=\"catch unExcepted Exception\", url=\"%s\", product_id=\"%s\", request_id=\"%s\", service_name=\"%s\", ts_id=\"%s\", error=\"%s\", stackTrace=\"%s\"", url, productId, requestId, serviceName, tsId, e.getMessage(), getStackTrace(e));
        LOGGER.error(err, e);

        Status responseStatus = Status.INTERNAL_ERROR;
        responseStatus.setMessage(e.getMessage() == null ? getStackTrace(e) : e.getMessage());
        return Mono.just(new MartResponse(responseStatus,
                new MartData(new ArrayList<>())));
    }

    private String getStackTrace(Exception e) {
        if(e.getStackTrace() == null) {
            return "";
        }
        try (StringWriter stringWriter = new StringWriter();
            PrintWriter printWriter  = new PrintWriter(stringWriter)) {
            e.printStackTrace(printWriter);
            return stringWriter.toString();
        } catch (Exception ex) {
            return "could not get stacktrace";
        }
    }

    private String extractProductId(ServerWebExchange ex) {
        String productId = "";
        List<String> ids = ex.getRequest().getHeaders().get(X_API_PRODUCT_ID);
        if(ids != null && !ids.isEmpty()) {
            productId = ids.get(0);
        }
        return productId;
    }

    private String extractRequestId(ServerWebExchange ex) {
        String requestId = "";
        List<String> ids = ex.getRequest().getHeaders().get(X_API_REQUEST_ID);
        if(ids != null && !ids.isEmpty()) {
            requestId = ids.get(0);
        }
        return requestId;
    }

    private String extractServiceName(ServerWebExchange ex) {
        String requestId = "";
        List<String> ids = ex.getRequest().getHeaders().get(X_API_SERVICE_NAME.toLowerCase());
        if(ids != null && !ids.isEmpty()) {
            requestId = ids.get(0);
        }
        return requestId;
    }
    private String extractTsId(ServerWebExchange ex) {
        String requestId = "";
        List<String> ids = ex.getRequest().getHeaders().get(X_API_TS_ID.toLowerCase());
        if(ids != null && !ids.isEmpty()) {
            requestId = ids.get(0);
        }
        return requestId;
    }
}