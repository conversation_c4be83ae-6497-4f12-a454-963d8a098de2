package com.morningstar.martapi.filter;

import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import reactor.util.context.Context;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.AUTHORIZATION_CONTEXT_KEY;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.CONTEXT_MAP;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WebHeaderFilterTest {

    @Test
    public void shouldCustomizeTest() {
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerHttpResponse response = mock(ServerHttpResponse.class);
        ServerWebExchange serverWebExchange = mock(ServerWebExchange.class);
        WebFilterChain chain = mock(WebFilterChain.class);
        List<String> idList = Arrays.asList("F0GBR04E6W");;

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(request.getHeaders()).thenReturn(httpHeaders);
        ServerHttpRequest.Builder builder = mock(ServerHttpRequest.Builder.class);
        when(builder.header(any(), any())).thenReturn(builder);
        when(request.mutate()).thenReturn(builder);
        when(builder.build()).thenReturn(request);

        when(serverWebExchange.getRequest()).thenReturn(request);
        when(serverWebExchange.getResponse()).thenReturn(response);
        when(chain.filter(any())).thenReturn(Mono.empty());
        ServerWebExchange.Builder exBuilder = mock(ServerWebExchange.Builder.class);
        when(serverWebExchange.mutate()).thenReturn(exBuilder);
        when(exBuilder.build()).thenReturn(serverWebExchange);
        when(exBuilder.request(any(ServerHttpRequest.class))).thenReturn(exBuilder);
        WebHeaderFilter webHeaderFilter = new WebHeaderFilter();
        webHeaderFilter.filter(serverWebExchange,chain);
    }

    @Test
    public void testAddRequestHeadersToContext(){
        WebHeaderFilter webHeaderFilter = new WebHeaderFilter();
        // Prepare the mock request
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        HttpHeaders headers = new HttpHeaders();
        headers.add("x-api-request-id", "AAA");
        headers.add("authorization", "Bearer some-token");

        when(request.getHeaders()).thenReturn(headers);

        // Create a context to pass
        Context context = Context.empty();

        // Call the method under test
        Context resultContext = webHeaderFilter.addRequestHeadersToContext(request, context);

        // Extract the context map to verify
        Map<String, String> contextMap = resultContext.get(CONTEXT_MAP);

        // Assertions
        assertNotNull(contextMap);
        assertEquals("AAA", contextMap.get("x-api-request-id"));  // Check for custom header
        assertEquals("Bearer some-token", contextMap.get(AUTHORIZATION_CONTEXT_KEY)); // Check for authorization header
    }

    @Test
    public void testAddsContextToResponseHeaders() {
        WebHeaderFilter webHeaderFilter = new WebHeaderFilter();
        // Prepare a mock response
        ServerHttpResponse response = mock(ServerHttpResponse.class);
        HttpHeaders responseHeaders = new HttpHeaders();
        when(response.getHeaders()).thenReturn(responseHeaders);

        // Prepare the context with headers to be added
        Map<String, String> contextMap = new HashMap<>();
        contextMap.put("x-api-request-id", "AAA");
        contextMap.put("x-api-user-id", "BBB");
        contextMap.put("authorization", "Bearer CCC");
        // Not adding "authorization" to test its exclusion

        // Create the reactor context and put CONTEXT_MAP
        Context context = Context.of(CONTEXT_MAP, contextMap);

        // Call the method with the mocked response
        Mono<Void> result = webHeaderFilter.addContextToHttpResponseHeaders(response)
                .contextWrite(ctx -> context);

        // Subscribe to the Mono to trigger the operation
        result.subscribe();

        // Verify that the expected headers are added to the response
        verify(response).getHeaders();
        assertEquals("AAA", responseHeaders.getFirst("x-api-request-id"));
        assertEquals("BBB", responseHeaders.getFirst("x-api-user-id"));
        assertNull(responseHeaders.get("authorization"));
    }
}
