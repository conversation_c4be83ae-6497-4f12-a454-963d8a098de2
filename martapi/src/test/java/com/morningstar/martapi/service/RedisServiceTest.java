package com.morningstar.martapi.service;

import com.morningstar.martapi.entity.AsyncCacheMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceTest {
    @Mock
    RedisTemplate<String, String> redisTemplate;

    RedisService redisService;

    @Test
    public void testRedis() throws Exception{
        redisService = new RedisService(redisTemplate);
        ValueOperations<String,String> valueOp = mock(ValueOperations.class);
        doNothing().when(valueOp).set(anyString(), anyString(), any());
        when(redisTemplate.opsForValue()).thenReturn(valueOp);
        AsyncCacheMessage msg = new AsyncCacheMessage();
        msg.setProductId("prod1");
        msg.setUserId("user1");
        msg.setStatus("Submitted");
        redisService.addRedisKey("job1", msg);
        verify(valueOp, times(1)).set(anyString(), anyString(), any());
    }

    @Test
    public void testIsJobInactive() {
        redisService = new RedisService(redisTemplate);
        ValueOperations<String,String> valueOp = mock(ValueOperations.class);
        when(redisTemplate.opsForValue()).thenReturn(valueOp);
        redisService.isJobInactive("job1");
        verify(valueOp, times(1)).get(anyString());
    }
}
