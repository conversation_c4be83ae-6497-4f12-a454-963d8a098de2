package com.morningstar.martapi.service;

import com.morningstar.martapi.entity.AsyncCacheMessage;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import javax.inject.Named;
import java.time.Duration;

@Named
public class RedisService {
    private RedisTemplate<String, String> redisTemplate;

    public RedisService(@Named("syncCacheDataTemplate") RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void addRedisKey(String jobId, AsyncCacheMessage cacheMessage) {
        redisTemplate.opsForValue().set(String.join(":", "async", jobId), JsonUtils.toJsonString(cacheMessage), Duration.ofMinutes(60));
    }

    public String getRedisKey(String jobId) {
        return redisTemplate.opsForValue().get(String.join(":", "async", jobId));
    }

    public String getTotalPages(String jobId) {
        return redisTemplate.opsForValue().get(String.join(":", "async", jobId, "pages"));
    }

    public boolean isJobInactive(String jobId) {
        return (StringUtils.isNullOrEmpty(redisTemplate.opsForValue().get(String.join("_", "async_hb", jobId))));
    }
}
