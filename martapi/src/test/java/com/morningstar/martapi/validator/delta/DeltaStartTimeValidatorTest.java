package com.morningstar.martapi.validator.delta;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

import static com.morningstar.martapi.validator.delta.DeltaStartTimeValidator.DELTA_START_TIME_DAY_LIMIT;

public class DeltaStartTimeValidatorTest {

    private DeltaStartTimeValidator validator;

    @BeforeEach
    public void setup() {
        validator = new DeltaStartTimeValidator();
    }

    @Test
    public void validateDeltaStartTimeAfterPresent() {
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class, () -> validator.validate(buildFailRequest1()));
        Assertions.assertEquals(Status.INVALID_DELTA_START_TIME, exception.getStatus());
    }

    @Test
    public void validateNoDeltaStartTimeForDetections() {
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class, () -> validator.validate(buildFailRequest2()));
        Assertions.assertEquals(Status.INVALID_DELTA_START_TIME, exception.getStatus());
    }

    @Test
    public void validateDeltaStartOutsideDayLimit() {
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class, () -> validator.validate(buildFailRequest3()));
        Assertions.assertEquals(Status.INVALID_DELTA_START_TIME, exception.getStatus());
    }

    @Test
    public void validate() {
        validator.validate(buildRequest());
    }


    private InvestmentApiRequest buildFailRequest1() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now().plus(Duration.ofDays(5)))
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

    private InvestmentApiRequest buildFailRequest2() {
        return InvestmentApiRequest.builder()
                .isDeltaDetection(true)
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

    private InvestmentApiRequest buildFailRequest3() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now().minus(Duration.ofDays(DELTA_START_TIME_DAY_LIMIT + 10)))
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

    private InvestmentApiRequest buildRequest() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now().minus(Duration.ofDays(5)))
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }
}
