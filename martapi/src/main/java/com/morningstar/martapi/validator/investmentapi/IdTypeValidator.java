package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class IdTypeValidator implements Validator<InvestmentApiRequest> {

    private static final Set<String> VALID_ID_TYPES = Set.of(
            "MorningstarIndustryCode",
            "CompanyId",
            "StyleBoxRegionId"
    );

    @Override
    public void validate(InvestmentApiRequest request) throws RuntimeException {
        String idType = request.getIdType();
        if (StringUtils.isNotEmpty(idType) && !VALID_ID_TYPES.contains(idType)) {
            throw new InvestmentApiValidationException(Status.INVALID_ID_TYPE);
        }
    }
}
