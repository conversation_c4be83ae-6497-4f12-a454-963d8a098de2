package com.morningstar.martapi.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.dataac.martgateway.core.async.entity.AsyncInput;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AsyncCacheMessage {
    String userId;
    String productId;
    String status;
    String startTime;
    String endTime;
    String url;
    String urlExpireTime;
    String errorMessage;
    String environment;
    String s3region;
    AsyncInput.ApiType apiType;
    String totalPages;
    Integer blBatchSize;
    Integer ltBatchSize;
}
