package com.morningstar.martapi.exception;

import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.martgateway.domains.index.entity.IndexReferenceDataResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.net.URI;
import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.Mockito.when;

public class GridViewExceptionHandlerTest {

    private GridViewExceptionHandler handler;
    private ServerWebExchange serverWebExchange;


    @Before
    public void setup() {
        handler = new GridViewExceptionHandler();

        ServerHttpRequest httpRequest = Mockito.mock(ServerHttpRequest.class);
        serverWebExchange = Mockito.mock(ServerWebExchange.class);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId", List.of("MDS"));
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(URI.create("test1"));
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
    }

    @Test
    public void handleInvalidRequestInputTest() {


        ResponseEntity<InvestmentResponse> responseEntity = handler.handleInvalidRequestInput(new DecodingException("JSON error"), serverWebExchange);
        Assertions.assertEquals(HttpStatus.BAD_REQUEST,responseEntity.getStatusCode());
    }

    @Test
    public void handleValidationExceptionTest() {
        ResponseEntity<InvestmentResponse> responseEntity = handler.handleValidationException(new InvestmentApiValidationException(Status.EXPIRED_TOKEN), serverWebExchange);
        Assertions.assertEquals(HttpStatus.UNAUTHORIZED,responseEntity.getStatusCode());
    }

    @Test
    public void handleBadRequestExceptionTest() {
        Exception e = new IllegalArgumentException("FrequencyType parameter is missing.");
        ResponseEntity<InvestmentResponse> response = handler.handleBadRequestException(
                e, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400");
        assertEquals(response.getBody().getStatus().getMessage(), "FrequencyType parameter is missing.");
    }

    @Test
    public void handleEntitlementExceptionTest() {
        EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);
        ResponseEntity<InvestmentResponse> response = handler.handleEntitlementException(
                e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "500300");
        assertEquals(response.getBody().getStatus().getMessage(), "Redis connection failed");
    }

    @Test
    public void handleEntitlementExceptionForTranscriptTest() {
        EntitlementException e = new EntitlementException(Status.NO_ENTITLEMENT_INFO, "User is not entitled to access the package", "request_id");
        ResponseEntity<InvestmentResponse> response = handler.handleEntitlementException(
                e, serverWebExchange);
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "403");
        assertEquals(response.getBody().getStatus().getMessage(), "No entitlement info");
    }

    @Test
    public void handleOtherException() {
        Exception e = new Exception("Internal Server error");
        ResponseEntity<InvestmentResponse> response = handler.handleOtherException(
                e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "500");
        assertEquals(response.getBody().getStatus().getMessage(), "Internal Server error");
    }

    @Test
    public void handleNotFoundExceptionTest() {
        TranscriptApiException e = new TranscriptApiException("Error making POST request to Transcript API, Data not found for DataPointID EQN3D", HeadersAndParams.builder().requestId("reqyest_id").productId("product_id").authorizationToken("token").build(), "userID", new Throwable());
        ResponseEntity<InvestmentResponse> response = handler.handleNotFoundException(
                e, serverWebExchange);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "404");
        assertEquals(response.getBody().getStatus().getMessage(), "Error making POST request to Transcript API, Data not found for DataPointID EQN3D - java.lang.Throwable");
    }

    @Test
    public void handleInternalExceptionTest() {
        IndexAPIException e = new IndexAPIException("Internal Error");
        ResponseEntity<InvestmentResponse> response = handler.handleIndexAPIException(e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "500");
        assertEquals(response.getBody().getStatus().getMessage(), "Internal Error");
    }
}
