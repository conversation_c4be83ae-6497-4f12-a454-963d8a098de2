package com.morningstar.martapi.controller;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.service.MartGateway;

import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.UUID;

public class InvestmentControllerTest {


    private MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway;
    private InvestmentController investmentController;

    @Before
    public void setup() {
        gridViewGateway = mock(MartGateway.class);
        investmentController = new InvestmentController(gridViewGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void getAllData() {
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(new InvestmentResponse()));
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        Mono<?> invalid = investmentController.getData(investmentApiRequest, "", "", createToken(), UUID.randomUUID().toString(), "MDS", "true", "",false,"","", "", "");
        StepVerifier.create(invalid)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                });
    }
    @Test
    public void getAllDataErrorTest() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<?> invalid = investmentController.getData(investmentApiRequest, "", "", createToken(), UUID.randomUUID().toString(), "MDS", "true", "",false,"","", "", "");
        invalid.subscribe();
    }
    @Test
    public void getAllData2() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(new InvestmentResponse()));
        Mono<?> invalid = investmentController.getData(investmentApiRequest, "", "", createToken(),
                UUID.randomUUID().toString(), "MDS", "true", "",false,"","", "", "");
        StepVerifier.create(invalid)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                }).verifyComplete();
    }

    @Test
    public void getAllData3() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        when(gridViewGateway.asyncRetrieveSecurities(any())).thenAnswer(invocation ->  {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return Mono.just(new InvestmentResponse());
        });
        Mono<?> invalid = investmentController.getData(investmentApiRequest, "", "", createToken(),
                UUID.randomUUID().toString(), "MDS", "true", "",false,"","", "", "");
        StepVerifier.create(invalid)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                }).verifyComplete();
    }

    @Test
    public void keepStatusIfError() {
        InvestmentResponse response = new InvestmentResponse();
        response.setStatus(Status.BAD_REQUEST);
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase("view")
                .investments(Collections.singletonList(Investment.builder().id("F0000USA").build()))
                .dataPoints(Collections.singletonList(GridviewDataPoint.builder().dataPointId("HS0100").build()))
                .build();
        when(gridViewGateway.asyncRetrieveSecurities(investmentApiRequest)).thenReturn(Mono.just(response));
        Mono<InvestmentResponse> error = investmentController.getData(investmentApiRequest, "", "", createToken(),
                UUID.randomUUID().toString(), "MDS", "true", "",false,"","", "", "");

        StepVerifier.create(error)
                .expectSubscription()
                .assertNext(r -> Assert.assertEquals(Status.BAD_REQUEST, r.getStatus()))
                .verifyComplete();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }
}
