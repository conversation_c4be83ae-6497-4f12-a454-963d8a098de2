package com.morningstar.martapi.exception;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;
import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import java.net.URI;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class TsExceptionHandlerTest {

	private TsExceptionHandler handler;
	private ServerWebExchange serverWebExchange;
	@Before
	public void setup() {
		handler = new TsExceptionHandler();
	}

	@Test
	public void handleValidationExceptionTest() {
		ServerHttpRequest httpRequest = Mockito.mock(ServerHttpRequest.class);
		ServerWebExchange serverWebExchange = Mockito.mock(ServerWebExchange.class);
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.addAll("X-Api-ProductId", List.of("MDS"));
		when(serverWebExchange.getRequest()).thenReturn(httpRequest);
		when(httpRequest.getURI()).thenReturn(URI.create("test1"));
		when(httpRequest.getHeaders()).thenReturn(httpHeaders);

		ResponseEntity<InvestmentResponse> responseEntity = handler.handleValidationException(new TsGridViewValidationException(Status.EXPIRED_TOKEN), serverWebExchange);
		Assertions.assertEquals(HttpStatus.UNAUTHORIZED,responseEntity.getStatusCode());


		ResponseEntity<TSResponse> responseEntity2 = handler.handleValidationException(new TsCacheApiValidationException(Status.INVALID_PRODUCT_ID), serverWebExchange);
		Assertions.assertEquals(HttpStatus.BAD_REQUEST,responseEntity2.getStatusCode());


		ResponseEntity<TsCacheDataForProtoBuf.TimeSeriesDatas> responseEntity3 = handler.handleValidationException(new TsCacheProtobufValidationException(Status.INVALID_DATE_SETTING), serverWebExchange);
		Assertions.assertEquals(HttpStatus.BAD_REQUEST,responseEntity3.getStatusCode());
	}

	@Test
	public void handleEntitlementException_ProtobufFormat() throws Exception {
		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add("format", "0");
		mockRequestWithParams(queryParams);

		EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);

		ResponseEntity<?> response = handler.handleEntitlementException(e, serverWebExchange);

		assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
		assertTrue(response.getBody() instanceof TsCacheDataForProtoBuf.TimeSeriesDatas);
	}

	@Test
	public void handleEntitlementException_TsCacheFormat() throws Exception {
		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add("format", "1");
		mockRequestWithParams(queryParams);

		EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);

		ResponseEntity<?> response = handler.handleEntitlementException(e, serverWebExchange);

		assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
		assertTrue(response.getBody() instanceof TSResponse);
	}

	@Test
	public void handleEntitlementException_JsonFormat() throws Exception {
		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add("format", "json");
		mockRequestWithParams(queryParams);

		EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);

		ResponseEntity<?> response = handler.handleEntitlementException(e, serverWebExchange);

		assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
		assertTrue(response.getBody() instanceof InvestmentResponse);
	}

	@Test
	public void handleEntitlementException_DefaultCase() throws Exception {
		mockRequestWithParams(new LinkedMultiValueMap<>());

		Status testStatus = Status.REDIS_CONNECTION_FAILED;
		EntitlementException e = new EntitlementException(testStatus, null);

		ResponseEntity<?> response = handler.handleEntitlementException(e, serverWebExchange);

		assertEquals(extractStatusCode(testStatus), response.getStatusCode());
		assertEquals(testStatus, response.getBody());
	}

	private void mockRequestWithParams(MultiValueMap<String, String> queryParams) {
		serverWebExchange = Mockito.mock(ServerWebExchange.class);
		ServerHttpRequest httpRequest = Mockito.mock(ServerHttpRequest.class);

		URI uri = URI.create("http://localhost/test?format=0");
		when(httpRequest.getURI()).thenReturn(uri);
		when(httpRequest.getQueryParams()).thenReturn(queryParams);

		HttpHeaders headers = new HttpHeaders();
		headers.add("X-Api-ProductId", "TEST_PROD");
		when(httpRequest.getHeaders()).thenReturn(headers);

		when(serverWebExchange.getRequest()).thenReturn(httpRequest);
	}
}
